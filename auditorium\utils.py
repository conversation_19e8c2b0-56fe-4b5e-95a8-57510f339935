from public_resources.models import VideoAssetRecord

from django.conf import settings

import mux_python, datetime


# Authentication Setup
configuration = mux_python.Configuration()
configuration.username = settings.MUX_TOKEN_ID
configuration.password = settings.MUX_TOKEN_SECRET

# API Client Initialization
assets_api = mux_python.AssetsApi(mux_python.ApiClient(configuration))

def save_asset_record(instance, event):
    asset_record = VideoAssetRecord.objects.create(event=event, mux_asset_id=instance.mux_asset_id, mux_playback_id=instance.mux_playback_id, video_url=instance.video_url, video_uuid=instance.id)


def create_mux_asset(instance, event):

    input_settings = [mux_python.InputSettings(url=instance.video_file.url)]
    
    create_asset_request = mux_python.CreateAssetRequest(input=input_settings, playback_policy = [mux_python.PlaybackPolicy.PUBLIC], mp4_support='standard')
    create_asset_response = assets_api.create_asset(create_asset_request)
    instance.mux_asset_id = create_asset_response.data.id
    instance.mux_playback_id = create_asset_response.data.playback_ids[0].id
    instance.video_url = f'https://stream.mux.com/{create_asset_response.data.playback_ids[0].id}.m3u8'
    instance.mux_preview_url = f'https://stream.new/v/{create_asset_response.data.playback_ids[0].id}'
    instance.data = create_asset_response.data.to_dict()
    instance.save()
    save_asset_record(instance, event)


def get_asset_response(instance):
    get_asset_response = assets_api.get_asset(instance.mux_asset_id)


def delete_mux_asset(instance):
    delete_asset = assets_api.delete_asset(instance.mux_asset_id)


def add_asset_track(instance):
    add_captions = mux_python.CreateTrackRequest(url=instance.subtitles_file.url, type="text", text_type="subtitles", language_code=instance.language_code, closed_captions=instance.cc_enabled, name=instance.ref)
    caption_track = assets_api.create_asset_track(instance.video.mux_asset_id, add_captions)
    print(caption_track)
    instance.mux_track_id = caption_track.data.id
    instance.save()


def delete_asset_track(instance):
    delete_asset_track = assets_api.delete_asset_track(instance.video.mux_asset_id, instance.mux_track_id)
    

def get_total_seconds(stringHMS):
    timedeltaObj = datetime.datetime.strptime(str(stringHMS), "%H:%M:%S") - datetime.datetime(1900,1,1)
    return timedeltaObj.total_seconds()