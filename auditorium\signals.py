from auditorium.models import Video, VideoSubtitlesFile
from auditorium.utils import create_mux_asset, delete_mux_asset, add_asset_track, delete_asset_track
from helpers.tenants.tenant_helpers import get_current_tenant

from django.dispatch import receiver
from django.db.models.signals import post_save, pre_delete



@receiver(post_save, sender=Video)
def create_video(sender, instance, created, update_fields, raw, **kwargs):
    if (created or not instance.mux_asset_id) and instance.video_file:
        event = get_current_tenant()
        create_mux_asset(instance, event)


@receiver(pre_delete, sender=Video)
def pre_delete_mux_asset(sender, instance, using, **kwargs):
    if instance.mux_asset_id:
        delete_mux_asset(instance)



@receiver(post_save, sender=VideoSubtitlesFile)
def add_video_subtitles(sender, instance, created, update_fields, raw, **kwargs):
    if created or not instance.mux_track_id:
        add_asset_track(instance)


@receiver(pre_delete, sender=VideoSubtitlesFile)
def delete_video_subtitles(sender, instance, using, **kwargs):
    if instance.mux_track_id:
        delete_asset_track(instance)