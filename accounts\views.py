from .models import Account, Access, AccessSet
from .serializers import AccountSerializer, AccessSerializer, AccessSetSerializer
from rest_framework import status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.views import APIView
from rest_framework_api_key.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.http import Http404
from rest_framework.decorators import api_view, permission_classes
from helpers.permissions.permissions import VerifyToken



class AccountList(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get(self, request, format=None, **kwargs):
        user = request.user
        if user.is_staff:
            accounts = Account.objects.all().order_by('time_created')
        else:
            accounts = Account.objects.filter(access_sets__in=user.access_sets.all()).order_by('time_created')
        serializer = AccountSerializer(accounts, many=True, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = AccountSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AccountDetail(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get_object(self, pk, **kwargs):
        try:
            return Account.objects.get(pk=pk)
        except Account.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        account = self.get_object(pk)
        serializer = AccountSerializer(account, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        account = self.get_object(pk)
        serializer = AccountSerializer(account, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        account = self.get_object(pk)
        account.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class FilterAccountsByUser(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get(self, request, user_id, format=None, **kwargs):
        text = request.GET.get('text', '')
        accounts = Account.objects.filter(access_sets__user__id=user_id)
        if text:
            accounts = accounts.filter(name__icontains=text)
        serializer = AccountSerializer(accounts, many=True, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)


class AccessList(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get(self, request, format=None):
        accesses = Access.objects.all()
        serializer = AccessSerializer(accesses, many=True, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)
    
    def post(self, request, format=None, **kwargs):
        serializer = AccessSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AccessDetail(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get_object(self, pk, **kwargs):
        try:
            return Access.objects.get(pk=pk)
        except Access.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        access = self.get_object(pk)
        serializer = AccessSerializer(access, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        access = self.get_object(pk)
        serializer = AccessSerializer(access, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk, format=None, **kwargs):
        access = self.get_object(pk)
        access.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class AccessSetList(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get(self, request, format=None):
        access_sets = AccessSet.objects.all()
        serializer = AccessSetSerializer(access_sets, many=True, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)
    
    def post(self, request, format=None, **kwargs):
        serializer = AccessSetSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class AccessSetDetail(APIView):
    permission_classes = (IsAuthenticated | HasAPIKey,)

    def get_object(self, pk, **kwargs):
        try:
            return AccessSet.objects.get(pk=pk)
        except AccessSet.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        access_set = self.get_object(pk)
        serializer = AccessSetSerializer(access_set, context={'user': request.user})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        access_set = self.get_object(pk)
        serializer = AccessSetSerializer(access_set, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
    def delete(self, request, pk, format=None, **kwargs):
        access_set = self.get_object(pk)
        access_set.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class FilterAccessSetsByUser(APIView):
    permission_classes = (IsAuthenticated & VerifyToken | HasAPIKey,)

    def get(self, request, user_id, format=None, **kwargs):
        try:
            access_sets = AccessSet.objects.filter(user__id=user_id)
        except Exception as e:
            raise Http404 from e
        serializer = AccessSetSerializer(access_sets, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
