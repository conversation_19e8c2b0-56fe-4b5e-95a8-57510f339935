# Generated by Django 3.0.9 on 2021-02-12 15:49

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ConfirmationPage',
            fields=[
                ('id', models.AutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('logo', models.ImageField(upload_to='confirmation_logos')),
                ('background_image', models.ImageField(blank=True, null=True, upload_to='confirmation_background')),
                ('background_color', models.CharField(blank=True, max_length=9, null=True)),
                ('confirmation_header', models.CharField(max_length=250)),
                ('confirmation_text', models.TextField()),
                ('link_text', models.CharField(blank=True, max_length=100, null=True)),
            ],
        ),
    ]
