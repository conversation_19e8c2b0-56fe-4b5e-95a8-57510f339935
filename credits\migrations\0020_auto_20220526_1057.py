# Generated by Django 3.2.13 on 2022-05-26 14:57

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('credits', '0019_alter_creditdefinition_bcc_email'),
    ]

    operations = [
        migrations.AlterField(
            model_name='creditdefinition',
            name='max_credits',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Maximum amount of credits to be earned in the certificate', max_digits=4, null=True),
        ),
        migrations.AlterField(
            model_name='creditdefinition',
            name='min_credits',
            field=models.DecimalField(blank=True, decimal_places=2, help_text='Minimum amount of credits to be earned in the certificate. Even if no criteria completed, if issued will be given this amount. Must be less than or equal to max credits', max_digits=4, null=True),
        ),
        migrations.AlterField(
            model_name='criteria',
            name='credit_value',
            field=models.DecimalField(decimal_places=2, max_digits=4),
        ),
    ]
