# Generated by Django 3.2.8 on 2021-11-24 18:37

from django.db import migrations
import tinymce.models


class Migration(migrations.Migration):

    dependencies = [
        ('credits', '0012_auto_20211124_1142'),
    ]

    operations = [
        migrations.AlterField(
            model_name='creditdefinition',
            name='email_template',
            field=tinymce.models.HTMLField(blank=True, default="<img src='https://i.ibb.co/5GR1s8y/Banner.png' alt='Red Banner' border='0' />\n<p style='color: #000000; font-size: 14px; font-weight: bold; margin-left: 10px;'>Dear {{first_name}},</p>\n<br />\n<p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>Congratulations on attending {{event}} and successfully earning the attached certificate.</p>\n<br />\n<p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>If you are having difficulty downloading the certificate, reach out to one of our team members at <a style='text-decoration: none; color: #a10c00; font-weight: bold;' href='mailto:<EMAIL>' target='_blank' rel='noopener'><EMAIL></a></p>\n<p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>Sincerely,</p>\n<p style='color: #a10c00; font-size: 14px; margin-top: 10px; font-weight: bold; margin-left: 10px;'>The Matchbox Team</p>\n<p style='color: #000000; font-size: 14px; margin-top: 20px;'><a href='https://matchboxvirtual.com/' target='_blank' rel='noreferrer noopener'><img style='width: 200px; height: 56px; margin-left: 10px;' src='https://i.ibb.co/804t9TG/01-MVM-logo.png' alt='01-MVM-logo' border='0' /></a></p>", null=True, verbose_name='Certificate Email Template'),
        ),
    ]
