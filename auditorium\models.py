import uuid

from tinymce import models as tinymce_models

from django.contrib.postgres.fields import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.db import models
from django.utils import timezone

from chat.models import Chat
from chat.utils import create_public_chat
from spaces.models import SpaceAwareModel
from subscriptions.models import Subscription

from auditorium.utils import get_total_seconds



VIDEO = 'VIDEO'
ZOOM = 'ZOOM'
AUD_CHOICES = [
    (VIDEO, 'Video'),
    (ZOOM, 'Zoom')
]

class Auditorium(SpaceAwareModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=300, unique=True, blank=False, null=True)
    aud_type = models.CharField(max_length=5, choices=AUD_CHOICES, default=VIDEO)
    start_date_time = models.DateTimeField(blank=True, null=True)
    end_date_time = models.DateTimeField(blank=True, null=True)
    chat = models.ForeignKey(
        Chat, on_delete=models.CASCADE, null=True, blank=True, related_name='auditoriums')
    image = models.ImageField(upload_to='auditorium_images', blank=True, null=True)
    image_alt_text = models.CharField(max_length=300, blank=True, null=True, help_text="Optionally add descriptive alternative text to the image. If blank title will be used.")
    description = models.TextField(null=True, blank=True, help_text="This feature will be deprecated on July 1 2021. Please use the widget for new events and only use this one to edit.")
    video_call_url = models.URLField(max_length=5000, blank=True, null=True, help_text='Only for auditoriums of type zoom. Needs to be filled in if enabled')
    conference_session_url = models.URLField(max_length=5000, blank=True, null=True, help_text='Only for auditoriums with at least check in enabled (check out may also be enabled). Needs to be filled in if enabled')
    is_on_demand = models.BooleanField(default=False)
    is_live_stream = models.BooleanField(default=False)
    change_room_on = models.BooleanField(default=True)
    chat_toggle = models.BooleanField(default=True)
    check_in = models.BooleanField(default=False, help_text='For conferences.io check in for CE credits')
    check_out = models.BooleanField(default=False)
    allow_list = models.ManyToManyField(
        Subscription, related_name='allowed_auditoriums', blank=True, help_text="if an attendee is added to this field the tile is considered allowed and will only show up for attendees who are on the allow list")
    host_email = models.CharField(max_length=200, null=True, blank=True, help_text="Auto filled field")
    meeting_id = models.CharField(max_length=100, null=True, blank=True, help_text="Auto filled field")
    html_blurb = tinymce_models.HTMLField(blank=True, null=True, verbose_name="Auditorium Description", help_text="Please use this editor to change text in the auditorium description in the lobby and chat info panel. If you use this editor the old description or blurb will be replaced.")
    order = models.IntegerField(default=1)
    time_created = models.DateTimeField(default=timezone.now)
    related_rooms = models.ManyToManyField(
        "self", blank=True, symmetrical=False)
    related_rooms_name = models.CharField(max_length=50, default="Related Content", help_text="Name for related rooms feature in this auditorum. Will not show if no related rooms selected.")
    
    objects = models.Manager()

    def __str__(self):
        return f'{self.name}: {str(self.id)}'

    def save(self, *args, **kwargs):
        if not self.chat:
            chat = create_public_chat(self.name)
            self.chat = chat
        # elif not self.chat.chat_channel:
        #     chat = create_channel(self.chat)
            self.chat = chat
        if not self.image_alt_text and self.image:
            self.image_alt_text = self.name
        super().save(*args, **kwargs)
        return self


class Video(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=500, blank=True, null=True)
    video_file = models.FileField(upload_to='auditorium_videos', blank=True, null=True)
    subtitles_file = models.FileField(upload_to='auditorium_video_subtitles', blank=True, null=True)
    mux_asset_id = models.CharField(max_length=100, blank=True, null=True)
    mux_playback_id = models.CharField(max_length=100, blank=True, null=True)
    video_url = models.URLField(max_length=1000, blank=True, null=True)
    duration = models.DurationField(null=True, blank=True, help_text="Please use the following format: <em>HH:MM:SS</em>.")
    auditorium = models.ForeignKey(
        Auditorium, on_delete=models.CASCADE, blank=False, related_name='videos')
    time_created = models.DateTimeField(default=timezone.now)
    seconds = models.IntegerField(blank=True, null=True, help_text="This field is auto filled, it is only for dev purposes.")
    cc_enabled = models.BooleanField(default=False)
    is_live_stream = models.BooleanField(default=False)
    mux_preview_url = models.URLField(max_length=1000, blank=True, null=True)
    order = models.IntegerField(default=1)
    data = HStoreField(default=dict)

    objects = models.Manager()

    def __str__(self):
        return self.name or f'{self.auditorium.name}: {self.video_url}'

    def save(self, *args, **kwargs):
        if self.duration:
            duration_time = self.duration
            self.seconds = get_total_seconds(duration_time)
        super(Video, self).save(*args, **kwargs)
        return self


LANGUAGE_CODES = [
    ('en', 'en'),
    ('es', 'es'),
    ('ca', 'ca'),
    ('fr', 'fr'),
    ('de', 'de'),
    ('ru', 'ru'),
    ('it', 'it'),
    ('da', 'da'),
    ('pt', 'pt'),
    ('ch', 'ch'),
    ('zh', 'zh'),
    ('nl', 'nl'),
    ('no', 'no'),
    ('uk', 'uk'),
    ('ja', 'ja'),
    ('ko', 'ko'),
    ('hr', 'hr'),
    ('fi', 'fi'),
    ('sv', 'sv'),
    ('pl', 'pl'),
    ('el', 'el'),
    ('hu', 'hu'),
    ('tr', 'tr'),
    ('cs', 'cs'),
    ('et', 'et'),
    ('di', 'di')
]

class VideoSubtitlesFile(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    ref = models.CharField(max_length=250, unique=True, blank=True, null=True, help_text='For internal use only')
    video = models.ForeignKey(
        Video, on_delete=models.CASCADE, blank=False, related_name='video_subtitles_file')
    subtitles_file = models.FileField(upload_to='auditorium_video_subtitles', blank=True, null=True)
    language_code = models.CharField(max_length=5, choices=LANGUAGE_CODES, default='en', verbose_name='language')
    cc_enabled = models.BooleanField(default=False)
    mux_track_id = models.CharField(max_length=100, blank=True, null=True)

    objects = models.Manager()

    def __str__(self):
        return self.ref

class Session(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    auditorium = models.OneToOneField(
        Auditorium, on_delete=models.CASCADE, blank=False, related_name='session')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField(blank=True, null=True)
    pre_roll = models.URLField(max_length=1000, blank=True, null=True)
    post_roll = models.URLField(max_length=1000, blank=True, null=True)
    time_created = models.DateTimeField(default=timezone.now)
    timezone = models.CharField(max_length=300, blank=True, null=True, help_text="Optionally to save timezone for session start date")

    objects = models.Manager()

    def __str__(self):
        return (self.auditorium.name)


PDF = 'PDF'
WEBSITE = 'URL'
TEXT = 'TEXT'
TAB_CHOICES = [
    (PDF, 'PDF'),
    (WEBSITE, 'Website'),
    (TEXT, 'Text')
]

class AuditoriumResource(models.Model):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tab_type = models.CharField(max_length=5, choices=TAB_CHOICES, default=PDF)
    name = models.CharField(max_length=100, blank=True, null=True)
    sub_header = models.CharField(max_length=100, blank=True, null=True)
    auditorium = models.ForeignKey(
        Auditorium, on_delete=models.CASCADE, related_name='resources', null=True)
    pdf_file = models.FileField(upload_to='aud_resource_files', blank=True, null=True)
    image = models.ImageField(upload_to='aud_resource_files', blank=True, null=True)
    url = models.URLField(max_length=200, blank=True, null=True)
    priority = models.IntegerField(blank=True, null=True)
    text = models.TextField(blank=True, null=True)
    time_created = models.DateTimeField(default=timezone.now)
    
    objects = models.Manager()

    def __str__(self):
        return f'{self.auditorium.name}: {self.name}'
