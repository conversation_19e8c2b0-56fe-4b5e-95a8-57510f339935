# Generated by Django 3.0.9 on 2021-01-04 20:56

import chat.models
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Chat',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('header_message', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('content', models.TextField(validators=[chat.models.validate_content_message])),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('deleted', models.<PERSON><PERSON>an<PERSON>ield(default=False)),
            ],
        ),
    ]
