from .models import Auditorium, Video, AuditoriumResource, Session, VideoSubtitlesFile
from expohall.serializers import TileSerializer, ScheduleTileSerializer
from rest_framework import serializers
from tags.utils import check_permissions
from credits.serializers import CriteriaSerializer


class SessionSerializer(serializers.ModelSerializer):
    class Meta:
        model = Session
        fields = "__all__"


class AuditoriumResourceSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuditoriumResource
        fields = "__all__"


class VideoSerializer(serializers.ModelSerializer):
    class Meta:
        model = Video
        fields = "__all__"


class VideoSubtitlesFileSerializer(serializers.ModelSerializer):

    class Meta:
        model = VideoSubtitlesFile
        fields = "__all__"


class CreateVideoSerializer(serializers.ModelSerializer):
    input = serializers.FileField(source='video_file')
    class Meta:
        model = Video
        fields = ['input']

    def to_representation(self, instance):
        ret = super(CreateVideoSerializer, self).to_representation(instance)
        ret['playback_policy'] = ['public']

        return ret


class AuditoriumSerializer(serializers.ModelSerializer):
    videos = serializers.SerializerMethodField()
    resources = AuditoriumResourceSerializer(many=True, required=False)
    session = SessionSerializer(required=False)

    class Meta:
        model = Auditorium
        fields = "__all__"
        # depth = 1

    def get_videos(self, instance):
        videos = instance.videos.all().order_by('order')
        return VideoSerializer(videos, many=True, required=False).data


class SingleAuditoriumSerializer(serializers.ModelSerializer):
    videos = serializers.SerializerMethodField()
    resources = AuditoriumResourceSerializer(many=True, required=False)
    session = SessionSerializer(required=False)
    related_rooms = serializers.SerializerMethodField()
    # criteria = CriteriaSerializer(many=True, required=False)
    criteria = serializers.SerializerMethodField()

    class Meta:
        model = Auditorium
        fields = "__all__"
        # depth = 1

    def get_videos(self, instance):
        videos = instance.videos.all().order_by('order')
        return VideoSerializer(videos, many=True, required=False).data

    def get_related_rooms(self, instance):
        if 'subscriber' in self.context.keys():
            subscriber = self.context['subscriber']
            all_related_rooms = instance.related_rooms.all()
            related_rooms = check_permissions(all_related_rooms, subscriber)
        else:
            related_rooms = instance.related_rooms.all()
        return AuditoriumSerializer(related_rooms, many=True, required=False).data
    
    def get_criteria(self, instance):
        criteria = instance.criteria.filter(active=True)
        return CriteriaSerializer(criteria, many=True, required=False).data

    def to_representation(self, instance):     
        ret = super().to_representation(instance)
        ret['criteria'] = list(filter(None, ret['criteria']))
        return ret


class AuditoriumScheduleSerializer(serializers.ModelSerializer):
    tiles = TileSerializer(many=True, required=False)

    class Meta:
        model = Auditorium
        exclude = ['related_rooms', 'allow_list', 'conference_session_url', 'is_live_stream', 'change_room_on', 'chat_toggle', 'check_in', 'check_out', 'host_email', 'meeting_id', 'order', 'time_created', 'chat', 'space']
