# Generated by Django 3.0.9 on 2021-09-20 16:21

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0010_subscription_chat_token'),
        ('auditorium', '0037_auto_20210917_1419'),
    ]

    operations = [
        migrations.AlterField(
            model_name='auditorium',
            name='allow_list',
            field=models.ManyToManyField(blank=True, help_text='if an attendee is added to this field the tile is considered allowed and will only show up for attendees who are on the allow list', related_name='allowed_auditoriums', to='subscriptions.Subscription'),
        ),
    ]
