

from . import views
from django.urls import path, re_path
from django.contrib.admin.views.decorators import staff_member_required

urlpatterns = [
    path('credits/', staff_member_required(views.CreditDefinitionList.as_view(), login_url='/login_client/'), name='definition_list'),
    path('credits_2/<pk>/', staff_member_required(views.CreditRecordList.as_view(), login_url='/login_client/'), name='record_list_2'),
    path('credits/<pk>/', staff_member_required(views.CreditRecordEditable.as_view(), login_url='/login_client/'), name='record_list'),
    path('complete_criteria/', views.create_completed_criteria, name='complete_criteria'),
    path('download_templates/<pk>/', staff_member_required(views.download_templates, login_url='/login_client/'), name='download_templates'),
    path('send_certificate/<pk>/', staff_member_required(views.send_single_certificate, login_url='/login_client/'), name='send_certificate'),
    path('send_all_certificates/<pk>/', staff_member_required(views.send_all_certificates, login_url='/login_client/'), name='send_all_certificates'),
    path('import_records/', views.CreditRecordUpload.as_view(), name='csv_import_records'),
    path('export_credit_records_data/<pk>/', staff_member_required(views.export_credit_records_data, login_url='/login_client/'), name='export_credit_records_data'),
    # re_path(r'export_credit_records_data/<pk>/$', staff_member_required(views.export_credit_records_data, login_url='/login_client/'), name='export_credit_records_data'),
]