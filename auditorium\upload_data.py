from auditorium.models import Auditorium, Video
from auditorium.multipart_upload import AmazonS3
from helpers.imports.universal_helpers import get_default_false, format_date_time, format_duration
from helpers.imports.spaces_helpers import get_spaces
from helpers.imports.auditorium_helpers import get_or_create_auditorium, get_auditorium_by_id, get_auditorium, get_video, create_video, get_or_create_session

from django.conf import settings
from django.contrib import messages
from django.core.files import File
from django.core.files.base import ContentFile
from django.shortcuts import render
from django.utils import timezone
from django.utils.dateparse import parse_duration

from rest_framework.views import APIView
from rest_framework import permissions
from PIL import Image
from urllib.parse import urlparse

import io, csv

class AuditoriumUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_auditorium.html'
        return render(request, template_name)
    
    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_auditorium.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')

        imported_auds = []
        bad_rows = []
        bad_related = []
        bad_spaces = []

        reader = csv.DictReader(file)
        for row in reader:
            try:
                space_names = row['space_names']
                aud_name = row['aud_name']
                aud_type = row['aud_type']
                is_on_demand = row['is_on_demand']
                chat_toggle = row['chat_toggle']
                start_date_time = row['start_date_time']
                end_date_time = row['end_date_time']
                image_alt_text = row['image_alt_text']
                html_blurb = row['html_blurb']
                video_call_url = row['video_call_url']
                order = row['order']
                related_rooms_name = row['related_rooms_name']
                related_rooms = row['related_rooms']
            except Exception:
                context = {
                    'message': 'Make sure all the headers are named correctly in your csv and try again',
                }
                return render(request, template_name, context)
            try:
                start_date_time = format_date_time(start_date_time) if start_date_time else timezone.now()
                end_date_time = format_date_time(end_date_time) if end_date_time else timezone.now()
                auditorium = get_or_create_auditorium(aud_name)
                if space_names != '':
                    space_list = space_names.split(' \ ')    
                    new_spaces = get_spaces(space_list)
                    if new_spaces.count() != len(space_list):
                        bad_spaces.append(auditorium)
                    auditorium.space.set(new_spaces)

                if aud_type not in [auditorium.aud_type, '']:
                    auditorium.aud_type = aud_type
                if is_on_demand != '':
                    auditorium.is_on_demand = get_default_false(is_on_demand)
                if chat_toggle != '':
                    auditorium.chat_toggle = get_default_false(chat_toggle)
                if start_date_time not in [auditorium.start_date_time, '']:
                    auditorium.start_date_time = start_date_time
                if end_date_time not in [auditorium.end_date_time, '']:
                    auditorium.end_date_time = end_date_time
                if image_alt_text not in [auditorium.image_alt_text, '']:
                    auditorium.image_alt_text = image_alt_text
                if html_blurb not in [auditorium.html_blurb, '']:
                    auditorium.html_blurb = html_blurb
                if video_call_url not in [auditorium.video_call_url, '']:
                    auditorium.video_call_url = video_call_url
                if order not in [auditorium.order, '']:
                    auditorium.order = order
                if related_rooms_name not in [auditorium.related_rooms_name, '']:
                    auditorium.related_rooms_name = related_rooms_name
                if related_rooms != '':
                    rooms_list = related_rooms.split(' \ ')
                    try:
                        new_related = Auditorium.objects.filter(name__in=rooms_list)
                        if new_related.count() != len(rooms_list):
                            bad_related.append(auditorium)
                        auditorium.related_rooms.set(new_related)
                    except Exception:
                        bad_related.append(auditorium)

                auditorium.save()
                imported_auds.append(aud_name)

            except Exception:
                bad_rows.append(aud_name)
            context = {
                'message': 'Upload Complete',
                'imported_auds': imported_auds,
                'bad_rows': bad_rows,
                'bad_related': bad_related,
                'bad_spaces': bad_spaces
            }
        return render(request, template_name, context)


class AuditoriumImageUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        auditoriums = Auditorium.objects.all()
        context = {
            'aud_list': auditoriums,
        }
        template_name = 'event_upload/import_auditorium_image.html'
        return render(request, template_name, context)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_auditorium_image.html'
        auditoriums = Auditorium.objects.all()
        aud_pic_io = io.BytesIO()
        try:
            img_file = self.request.FILES['aud-image-file']
        except Exception:
            context = {
                'message': 'No File Selected',
                'aud_list': auditoriums,
            }
            return render(request, template_name, context)

        aud_ids = self.request.data.getlist('aud-ids')
        for aud_id in aud_ids:
            auditorium = get_auditorium_by_id(aud_id)
            if auditorium:
                try:
                    image = Image.open(img_file)
                    image.save(aud_pic_io, format='png', save=False)
                    aud_pic_io.seek(0)
                    content_file = ContentFile(aud_pic_io.read())
                    file = File(content_file)
                    auditorium.image.save(img_file.name, file)
                except Exception:
                    context = { 
                        'message': 'There was a problem with the image file',
                        'aud_list': auditoriums,
                    }
                    return render(request, template_name, context)
            else:
                context = { 
                    'message': 'There was a problem with the selected auditorium(s). Please reload and try again',
                    'aud_list': auditoriums,
                }
                return render(request, template_name, context)
        context = {
            'message': 'Success!',
            'aud_list': auditoriums,
        }
        return render(request, template_name, context)


class VideoFileUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'auditoriums/video_file_upload.html'
        auditoriums = Auditorium.objects.all()
        context = {
            'auditoriums': auditoriums,
        }
        return render(request, template_name, context)

    def post(self, request, format=None, **kwargs):
        template_name = 'auditoriums/video_file_upload.html'
        auditoriums = Auditorium.objects.all()
        try:
            video_file = self.request.FILES['video-file']
        except Exception:
            context = {
                'message': 'No File Selected',
                'auditoriums': auditoriums
            }
            return render(request, template_name, context)

        try:
            auditorium = get_auditorium_by_id(request.data['auditorium-id'])
        except Exception:
            context = {
                'message': 'No Auditorium Selected',
                'auditoriums': auditoriums
            }
            return render(request, template_name, context)

        # print_task()
        # print('BEFORE - GET_AWS_DATA')
        # get_aws_data(request, video_file, auditorium)

        s3_resource = AmazonS3(aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY, region_name=settings.AWS_S3_REGION_NAME)

        bucket_name = settings.AWS_STORAGE_BUCKET_NAME
        get_url = self.request.META.get('HTTP_REFERER')
        get_sub_dom = urlparse(get_url)
        sub_domain = get_sub_dom.hostname.split('.')[0]
        file_bucket_location = f'media/{sub_domain}/auditorium_videos/{video_file.name}'

        s3_resource.push_data_to_s3_bucket(bucket_name, video_file, file_bucket_location, video_file.size, video_file.content_type)



        # create_auditorium_video(video_file, auditorium)
        auditorium_video = Video.objects.create(name=video_file.name, auditorium=auditorium, video_file=video_file)

        context = {
            'message': 'Success!',
            'auditoriums': auditoriums
        }
        return render(request, template_name, context)


class AuditoriumVideoUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_auditorium_video.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_auditorium_video.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        videos = []
        bad_rows = []
        reader = csv.DictReader(file)
        for row in reader:
            try:
                aud_name = row['aud_name']
                vid_name = row['vid_name']
                video_url = row['video_url']
                duration = row['duration']
                cc_enabled = get_default_false(row['cc_enabled'])
                live_stream = get_default_false(row['live_stream'])
                order = row['order']

                duration = parse_duration(format_duration(duration))
                auditorium = get_auditorium(aud_name)
                video = get_video(vid_name, auditorium)

                if video and aud_name != video.auditorium.name or not video:
                    video = create_video(vid_name, auditorium)
                if video_url not in [video.video_url, '']:
                    video.video_url = video_url
                if duration not in [video.duration, '']:
                    video.duration = duration
                if cc_enabled not in [video.cc_enabled, '']:
                    video.cc_enabled = cc_enabled
                if live_stream not in [video.is_live_stream, '']:
                    video.is_live_stream = live_stream
                if order not in [video.order, '']:
                    video.order = order
                video.save()

                videos.append(vid_name)

            except Exception:
                bad_rows.append(vid_name)

            context = {
                'message': 'Upload Complete!',
                'videos': videos,
                'bad_rows': bad_rows,
            }
        return render(request, template_name, context)


class AuditoriumSessionUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_auditorium_sessions.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_auditorium_sessions.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        imported_sessions = []
        bad_rows = []
        reader = csv.DictReader(file)
        for row in reader:
            try:
                aud_name = row['aud_name']
                start_date_time = row['start_date_time']
                start_date_time = format_date_time(start_date_time) if start_date_time else timezone.now()
                pre_roll_url = row['pre_roll_url']
                post_roll_url = row['post_roll_url']

                auditorium = get_auditorium(aud_name)
                session = get_or_create_session(auditorium, start_date_time)
                if auditorium and session:
                    if pre_roll_url != session.pre_roll:
                        session.pre_roll = pre_roll_url
                    if post_roll_url != session.post_roll:
                        session.post_roll = post_roll_url
                    session.save()

                imported_sessions.append(aud_name)
            except Exception:
                bad_rows.append(aud_name)

            context = {
                'message': 'Upload Completed!',
                'imported_sessions': imported_sessions,
                'bad_rows': bad_rows,
            }
        template_name = 'event_upload/import_auditorium_sessions.html'
        return render(request, template_name, context)