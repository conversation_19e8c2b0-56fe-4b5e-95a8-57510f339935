from rest_framework import viewsets, permissions, status
from .models import Chat, Message
from .serializers import Chat<PERSON>erializer, MessageSerializer
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from helpers.permissions.permissions import VerifyToken
from rest_framework.views import APIView
from django.http import Http404
from rest_framework_api_key.permissions import Has<PERSON><PERSON><PERSON>ey
from helpers.tenants.tenant_helpers import get_current_tenant
from rest_framework.decorators import permission_classes
from helpers.tenants.tenant_helpers import get_tenant_from_host
from django_tenants.utils import schema_context
from django.shortcuts import render
from django.http import StreamingHttpResponse, JsonResponse
from spaces.models import Space
import csv
from .utils import export_chat, get_member_count, format_members, export_chats
from django.contrib import messages
from expohall.models import Tile
from auditorium.models import Auditorium
import time
from io import StringIO

class ChatList(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        chats = Chat.objects.all()
        serializer = ChatSerializer(chats, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = ChatSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ChatDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return Chat.objects.get(pk=pk)
        except Chat.DoesNotExist:
            raise Http404

    def get(self, request, pk, format=None, **kwargs):
        chat = self.get_object(pk)
        serializer = ChatSerializer(chat)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        chat = self.get_object(pk)
        serializer = ChatSerializer(chat, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        chat = self.get_object(pk)
        chat.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class MessageList(APIView):
    permission_classes = (IsAuthenticated & VerifyToken | HasAPIKey,)

    def get(self, request, format=None, **kwargs):
        messages = Message.objects.all()
        serializer = MessageSerializer(messages, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = MessageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MessageDetail(APIView):
    permission_classes = (IsAuthenticated & VerifyToken | HasAPIKey,)

    def get_object(self, pk, **kwargs):
        try:
            return Message.objects.get(pk=pk)
        except Message.DoesNotExist:
            raise Http404

    def get(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        serializer = MessageSerializer(message)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        serializer = MessageSerializer(
            message, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        message.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class MessageListByChat(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        chat = self.kwargs.get('chat')
        messages = Message.objects.filter(chat=chat)
        serializer = MessageSerializer(messages, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = MessageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class MessageDetailByChat(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            chat = self.kwargs.get('chat')
            return Message.objects.get(pk=pk, chat=chat)
        except Message.DoesNotExist:
            raise Http404

    def get(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        serializer = MessageSerializer(message)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        serializer = MessageSerializer(
            message, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        message = self.get_object(pk)
        message.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)

# class PrivateChatList(APIView):
#     permission_classes = (IsAuthenticated, VerifyToken)

#     def get(self, request, format=None, **kwargs):
#         event = get_current_tenant()
#         user = request.user
#         chats = PrivateChat.objects.filter(participants=user, event=event)
#         serializer = PrivateChatSerializer(chats, many=True)
#         return Response(serializer.data, status=status.HTTP_200_OK)

#     def post(self, request, format=None, **kwargs):
#         event = get_current_tenant()
#         data = request.data
#         data['event'] = event.id
#         data['chat_channel'] = True
#         serializer = PrivateChatSerializer(data=data)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


# class PrivateChatDetail(APIView):
#     permission_classes = (IsAuthenticated, VerifyToken)

#     def get_object(self, pk, **kwargs):
#         try:
#             return PrivateChat.objects.get(pk=pk)
#         except PrivateChat.DoesNotExist:
#             raise Http404

#     def get(self, request, pk, format=None, **kwargs):
#         chat = self.get_object(pk)
#         serializer = PrivateChatSerializer(chat)
#         return Response(serializer.data, status=status.HTTP_200_OK)

#     def put(self, request, pk, format=None, **kwargs):
#         chat = self.get_object(pk)
#         serializer = PrivateChatSerializer(chat, data=request.data, partial=True)
#         if serializer.is_valid():
#             serializer.save()
#             return Response(serializer.data, status=status.HTTP_200_OK)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

#     def delete(self, request, pk, format=None, **kwargs):
#         chat = self.get_object(pk)
#         chat.delete()
#         return Response(status=status.HTTP_204_NO_CONTENT)
class ChatViewSet(viewsets.ModelViewSet):
    authentication_classes = ()
    permission_classes = [permissions.IsAuthenticated]
    """
    API endpoint that allows profiles to be viewed or edited.
    """
    queryset = Chat.objects.all()
    serializer_class = ChatSerializer


class MessageViewSet(viewsets.ModelViewSet):
    authentication_classes = ()
    permission_classes = [permissions.IsAuthenticated]
    """
    API endpoint that allows profiles to be viewed or edited.
    """
    queryset = Message.objects.all()
    serializer_class = MessageSerializer
    # permission_classes = [permissions.IsAuthenticated]

class Echo:
    """An object that implements just the write method of the file-like
    interface.
    """

    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value     

def iter_chat(chats, pseudo_buffer, schema_name, func, export_chat_id):
    with schema_context(schema_name):
        if func == 1:
            writer = csv.writer(pseudo_buffer)
            headers = [
                'Chat', 'Message', 'First name', 'Last name', 'Screen Name', 'Message Type', 'Reply Count', 'Reaction Count', 'Pinned By', 'Created at', 'Updated at'
            ]
            if export_chat_id:
                headers.insert(0, 'Chat_Id')
            yield writer.writerow(headers)
            
            chat_data = export_chats(chats)
            if not chat_data:
                row = ['Failure to load chat', '', '', '', '', '', '', '', '']
                if export_chat_id:
                    row.insert(0, '')
                yield writer.writerow(row)
            else:
                for chat_transcript in chat_data:
                    chat = chats.get(id=chat_transcript['channel']['id'])
                    if not chat_transcript['messages']:
                        row = [chat.name, 'No messages to show', '', '', '', '', '', '', '', '', '']
                        if export_chat_id:
                            row.insert(0, chat.id)
                        yield writer.writerow(row)
                        continue
                    for message in chat_transcript['messages']:
                        screen_name = ' '
                        if 'username' in message['user'].keys():
                            screen_name = message['user']['username']
                        row = [
                            chat.name, message['text'], message['user']['name'], message['user']['lastName'],
                            screen_name, message['type'], message['reply_count'],
                            message['reaction_counts'], message['pinned_by'], message['created_at'], message['updated_at']
                        ]
                        if export_chat_id:
                            row.insert(0, chat.id)
                        yield writer.writerow(row)
                        if 'replies' in message.keys():
                            for message in message['replies']:
                                screen_name = ' '
                                if 'username' in message['user'].keys():
                                    screen_name = message['user']['username']
                                row = [
                                    chat.name, message['text'], message['user']['name'], message['user']['lastName'],
                                    screen_name, message['type'], message['reply_count'],
                                    message['reaction_counts'], message['pinned_by'], message['created_at'], message['updated_at']
                                ]
                                if export_chat_id:
                                    row.insert(0, chat.id)
                                yield writer.writerow(row)

        elif func == 2:
            writer = csv.writer(pseudo_buffer)
            headers = [
                'Chat', 'Message Count','Member Count', 'Members']
            if export_chat_id:
                headers.insert(0, 'Chat_Id')
            yield writer.writerow(headers)
            chat_data = export_chats(chats)
            if not chat_data:
                row = ['Failure to load chat', '', '', '', '', '', '', '', '']
                if export_chat_id:
                    row.insert(0, '')
                yield writer.writerow(row)
            else:
                for chat_transcript in chat_data:
                    chat = chats.get(id=chat_transcript['channel']['id'])
                    if not chat_transcript:
                        row = [chat.name, 'Failure to load chat', '', '', '', '', '', '', '', '']
                        if export_chat_id:
                            row.insert(0, '')
                        yield writer.writerow(row)
                        continue
                    member_count = get_member_count(chat_transcript['channel'])
                    members = format_members(chat_transcript)
                    message_count = 0
                    if chat_transcript['messages']:
                        message_count = len(chat_transcript['messages'])
                    row = [chat.name, message_count, member_count, members]
                    if export_chat_id:
                        row.insert(0, '')
                    yield writer.writerow(row)
                    
class ChatExportList(APIView):
    permission_classes = (permissions.IsAdminUser, )

    def get(self, request):
        template_name = 'export/export_chats.html'
        return render(request, template_name)

    def post(self, request):
        template_name = 'export/export_chats.html'
        chat_option = request.data['chat-option']
        export_type = request.GET.get('type', 'csv').lower()
        
        event = get_tenant_from_host(request)
        if not event:
            res = {'error': 'Error parsing event'}
            return JsonResponse(res, status=400)
        with schema_context(event.schema_name):
            if chat_option == 'space-chats':
                func = 1
                spaces = Space.objects.filter(chat_toggle=True)
                chats = Chat.objects.filter(space__in=spaces)
                file_name = 'general_chats.csv'

            elif chat_option == 'tile-chats':
                func = 1
                tiles = Tile.objects.filter(chat_toggle=True)
                chats = Chat.objects.filter(tile__in=tiles)
                file_name = 'tile_chats.csv'

            elif chat_option == 'auditorium-chats':
                func = 1
                auditoriums = Auditorium.objects.filter(chat_toggle=True)
                chats = Chat.objects.filter(auditoriums__in=auditoriums)
                file_name = 'auditorium_chats.csv'

            elif chat_option == 'chat-summary':
                func = 2
                auditoriums = Auditorium.objects.filter(chat_toggle=True)
                tiles = Tile.objects.filter(chat_toggle=True)
                spaces = Space.objects.filter(chat_toggle=True)
               
                aud_chats = Chat.objects.filter(auditoriums__in=auditoriums)
                tile_chats = Chat.objects.filter(tile__in=tiles)
                space_chats = Chat.objects.filter(space__in=spaces)
                chats = aud_chats | tile_chats | space_chats
                file_name = 'chat_summary.csv'
                
            else:
                res = {'message': 'There was an error exporting the chats'}
                return render(request, template_name, res)
            
            chats = chats.distinct()
            if export_type == 'csv':
                response = StreamingHttpResponse(
                    streaming_content=(iter_chat(chats, Echo(), event.schema_name, func, False)),
                    content_type='text/csv',
                )
                response['Content-Disposition'] = 'attachment; filename=' + file_name
                return response
            else:
                chat_data = []
                for chat in iter_chat(chats, Echo(), event.schema_name, func, True):
                    chat_data.append(chat)
                json_data = parse_csv_to_json(chat_data)
                return JsonResponse(json_data, safe=False)
            

def parse_csv_to_json(csv_data):
    csv_string = ''.join(csv_data)
    csv_file = StringIO(csv_string)
    reader = csv.DictReader(csv_file)
    json_data = [row for row in reader]
    return json_data
