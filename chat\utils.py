
from .models import Cha<PERSON>
from django.conf import settings
from stream_chat import <PERSON><PERSON>hat
import threading
import time
import urllib.request, json 
from helpers.tenants.tenant_helpers import get_current_tenant

            

def create_public_chat(name):
    event = get_current_tenant()
    chat = Chat.objects.create(name=name)
    client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    client.update_user({"id": "system", "name": "The Server"})
    channel = client.channel("livestream", chat.id, {
        'name': chat.name,
        'team': event.frontend_sub_domain
    })
    channel.create("system")
    chat.chat_channel = True
    chat.save()
    return chat

def create_channel(chat):
    client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    client.update_user({"id": "system", "name": "The Server"})
    channel = client.channel("livestream", chat.id)
    channel.create("system")
    chat.chat_channel = True
    chat.save()
    return chat

def create_channel_with_team(chat, event):
    client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    client.update_user({"id": "system", "name": "The Server"})
    channel = client.channel("livestream", chat.id)
    channel.update({
        'name': chat.name,
        'team': event.frontend_sub_domain
    })
    
    chat.chat_channel = True
    chat.save()
    return chat

    

def create_private_chat(name, participants, owner):
    # chat = Chat.objects.create(name=name)
    # for participant in participants:
    #     chat.paricipants.add(participant)
    # chat.save()
    # client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    # channel = client.channel("messaging", chat.id, {
    #     'members': participants,
    #     'created_by_id': owner.id
    # })
    # channel.create()


    pass

def export_chats(chats):
    client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    channels = []
    
    chat_data = []
    for chat in chats:
        channel = {}
        channel['type'] = "livestream"
        channel['id'] = str(chat.id)
        channels.append(channel)
        if len(channels) == 25:
            data = get_chat_json(client,channels)
            if data:
                chat_data = chat_data+data
            channels = []
            continue
    if channels:
        data = get_chat_json(client,channels)
        chat_data = chat_data+data
    return chat_data

        
            

def get_chat_json(client,channels):
    response = client.export_channels(channels=channels)
    taskId = response['task_id']
    status_response = get_task_status(taskId, client)
    if status_response:
        with urllib.request.urlopen(status_response) as url:
            data = json.loads(url.read().decode())
            return data
    else:
        return False
    

def export_chat(chat):
    client = StreamChat(api_key=settings.STREAM_API_KEY, api_secret=settings.STREAM_API_SECRET)
    response = client.export_channel(channel_type="livestream",channel_id=str(chat.id) )
    # response = client.export_channel(channel_type="livestream",channel_id='8269aadf-1303-4dff-85fc-b7a39a78b753' )
    
    taskId = response['task_id']

    # t = set_interval(get_task_status,5 , taskId, client)
    status_response = get_task_status(taskId, client)
    if status_response:
        with urllib.request.urlopen(status_response) as url:
            data = json.loads(url.read().decode())
            return data[0]
    else:
        return False


def get_task_status(taskId, client):
    response = client.get_export_channel_status(taskId)

    if response['status'] == 'pending' or response['status'] == 'waiting' or response['status'] == 'running' :
        time.sleep(0.3)
        return get_task_status(taskId, client)
    elif response['status'] == 'completed':
        return response['result']['url']
    else:
        return False

def set_interval(func, sec, arg1, arg2): 
    def func_wrapper():
        t = set_interval(func, sec) 
        res = func(arg1, arg2) 
        # if res = '' 
    t = threading.Timer(sec, func_wrapper)
    t.start()
    return t

def get_member_count(chat_transcript):
    if 'member_count' in chat_transcript.keys():
        return chat_transcript['member_count']
    else:
        return 0

def format_members(chat_transcript):
    if not chat_transcript['members']:
        return ''
    else:
        members = []
        for member in chat_transcript['members']:
            screen_name = ''
            first_name = ''
            last_name = ''
            if 'username' in member['user'].keys():
                if member['user']['username']:
                    screen_name= ' ('+ member['user']['username']+')'
            if 'name' in member['user'].keys():
                first_name = member['user']['name']
            if 'lastName' in member['user'].keys():
                last_name  = member['user']['lastName']
            user = first_name + ' ' + last_name + screen_name
            members.append(user)
        member_list = ', '.join(map(str, members))
        return member_list