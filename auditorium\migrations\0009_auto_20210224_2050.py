# Generated by Django 3.0.9 on 2021-02-24 20:50

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0008_auto_20210219_1642'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditorium',
            name='chat_toggle',
            field=models.BooleanField(default=True),
        ),
        migrations.AddField(
            model_name='video',
            name='duration',
            field=models.TimeField(blank=True, null=True),
        ),
        migrations.CreateModel(
            name='Session',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('start_time', models.DateTimeField()),
                ('end_time', models.DateTimeField(blank=True, null=True)),
                ('pre_roll', models.URLField(blank=True, max_length=1000, null=True)),
                ('post_roll', models.URLField(blank=True, max_length=1000, null=True)),
                ('time_created', models.DateTimeField(default=django.utils.timezone.now)),
                ('auditorium', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='session', to='auditorium.Auditorium')),
            ],
        ),
    ]
