# Generated by Django 3.2.8 on 2022-03-03 18:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounts', '0002_invite'),
    ]

    operations = [
        migrations.CreateModel(
            name='Access',
            fields=[
                ('name', models.CharField(max_length=100, primary_key=True, serialize=False, unique=True)),
            ],
        ),
        migrations.AddField(
            model_name='invite',
            name='role',
            field=models.CharField(choices=[('ADMIN', 'Admin'), ('EDITOR', 'Editor')], default='ADMIN', max_length=50),
        ),
        migrations.AlterField(
            model_name='invite',
            name='status',
            field=models.CharField(choices=[('PENDING', 'Pending'), ('ACCEPTED', 'Accepted'), ('EXPIRED', 'Expired')], default='PENDING', max_length=50),
        ),
        migrations.AddField(
            model_name='invite',
            name='accesses',
            field=models.ManyToManyField(related_name='invitations', to='accounts.Access'),
        ),
        migrations.CreateModel(
            name='AccessSet',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('ADMIN', 'Admin'), ('EDITOR', 'Editor')], default='ADMIN', max_length=50)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('accesses', models.ManyToManyField(related_name='access_sets', to='accounts.Access')),
                ('account', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='access_sets', to='accounts.account')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('user', 'account')},
            },
        ),
    ]
