from django import template

register = template.Library()

def show_criteria(list):
    values = []
    for value in list:
        if value.criteria.name not in values:
            values.append(value.criteria.name)
    string = ", ".join(values)
    # if arg is first_name: return the first string before space

    return string

def get_credits_earned(record):
    earned_credits = 0
    max_credits = record.credit_definition.max_credits or 1000
    min_credits = record.credit_definition.min_credits or -1
    used_criteria = [] 
    for completed in record.completed_criteria.all():
        if completed.criteria.name not in used_criteria:
            earned_credits += completed.criteria.credit_value
            used_criteria.append(completed.criteria.name)
    highest_earned = min(earned_credits, max_credits)
    return max(min_credits, highest_earned)


register.filter('show_criteria', show_criteria)
register.filter('get_credits_earned', get_credits_earned)