from django.urls import path, include
from . import views


urlpatterns = [
    path('accounts/', include(([
        path('', views.AccountList.as_view()),
        path('<pk>/', views.AccountDetail.as_view()),
    ], 'accounts'))),

    path('accounts_filter/user/<str:user_id>/', views.FilterAccountsByUser.as_view(), name='filter_accounts_by_user'),
    
    path('accesses/', include(([
        path('', views.AccessList.as_view()),
        path('<pk>/', views.AccessDetail.as_view()),
    ], 'accesses'))),

    path('access_sets/', include(([
        path('', views.AccessSetList.as_view()),
        path('<pk>/', views.AccessSetDetail.as_view()),
    ], 'access_sets'))),

    path('access_sets_filter/user/<str:user_id>/', views.FilterAccessSetsByUser.as_view(), name='filter_access_sets_by_user'),
]
