from rest_framework import viewsets, permissions, status
from .models import Auditorium, Video, VideoSubtitlesFile, Session, AuditoriumResource
from expohall.models import Tile
from helpers.date_helpers import return_aud_dates, return_day_auds, get_now_datetime
from helpers.permissions.permissions import VerifyToken
from helpers.subscription.subscription_helpers import get_subscriber
from .serializers import AuditoriumSerializer, VideoSerializer, AuditoriumScheduleSerializer, SingleAuditoriumSerializer, VideoSubtitlesFileSerializer, SessionSerializer, AuditoriumResourceSerializer
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.views import APIView
from rest_framework.decorators import api_view, permission_classes
from rest_framework_api_key.permissions import <PERSON><PERSON><PERSON><PERSON><PERSON>
from django.http import Http404, HttpResponse
import datetime



class AuditoriumList(APIView):
    permission_classes = (IsAuthenticated & VerifyToken | HasAPIKey,)

    def get(self, request, format=None, **kwargs):
        if self.kwargs.get('space'):
            space = self.kwargs.get('space')
            auditoriums = Auditorium.objects.filter(
                space=space).order_by('order')
        else:
            auditoriums = Auditorium.objects.all().order_by('order')
        serializer = AuditoriumSerializer(auditoriums, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = AuditoriumSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AuditoriumDetail(APIView):
    permission_classes = (IsAuthenticated & VerifyToken | HasAPIKey,)

    def get_object(self, pk, **kwargs):
        try:
            return Auditorium.objects.get(pk=pk)
        except Auditorium.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        auditorium = self.get_object(pk)
        user = request.user
        subscriber = get_subscriber(user)
        if subscriber:
            serializer = SingleAuditoriumSerializer(
                auditorium, context={'subscriber': subscriber})
        else:
            serializer = SingleAuditoriumSerializer(auditorium)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        auditorium = self.get_object(pk)
        serializer = AuditoriumSerializer(
            auditorium, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        auditorium = self.get_object(pk)
        auditorium.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class AuditoriumResourceList(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        auditorium_resources = AuditoriumResource.objects.all()
        serializer = AuditoriumResourceSerializer(auditorium_resources, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = AuditoriumResourceSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class AuditoriumResourceDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return AuditoriumResource.objects.get(pk=pk)
        except AuditoriumResource.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        auditorium_resource = self.get_object(pk)
        serializer = AuditoriumResourceSerializer(auditorium_resource)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        auditorium_resource = self.get_object(pk)
        serializer = AuditoriumResourceSerializer(auditorium_resource, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        auditorium_resource = self.get_object(pk)
        auditorium_resource.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


@api_view(['GET'])
@permission_classes([IsAuthenticated, VerifyToken])
def auditorium_resources_filter_by_auditorium(request, **kwargs):
    if kwargs.get('auditorium'):
        auditorium_id = kwargs.get('auditorium')
        auditorium_resources = AuditoriumResource.objects.filter(auditorium_id=auditorium_id)
        serializer = AuditoriumResourceSerializer(auditorium_resources, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        auditorium_resources = AuditoriumResource.objects.none()
        serializer = AuditoriumResourceSerializer(auditorium_resources)
        return Response(serializer.data, status=status.HTTP_404_NOT_FOUND)


class SessionList(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        sessions = Session.objects.all()
        serializer = SessionSerializer(sessions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = SessionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class SessionDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return Session.objects.get(pk=pk)
        except Session.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        session = self.get_object(pk)
        serializer = SessionSerializer(session)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        session = self.get_object(pk)
        serializer = SessionSerializer(session, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        session = self.get_object(pk)
        session.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
    

@api_view(['GET'])
@permission_classes([IsAuthenticated, VerifyToken])
def sessions_filter_by_tile(request, **kwargs):
    if kwargs.get('tile'):
        tile_id = kwargs.get('tile')
        tile = Tile.objects.get(id=tile_id)
        sessions = Session.objects.filter(auditorium__tiles=tile)
        serializer = SessionSerializer(sessions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        sessions = Session.objects.none()
        serializer = SessionSerializer(sessions)
        return Response(serializer.data, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([IsAuthenticated, VerifyToken])
def sessions_filter_by_auditorium(request, **kwargs):
    if kwargs.get('auditorium'):
        auditorium_id = kwargs.get('auditorium')
        sessions = Session.objects.filter(auditorium=auditorium_id)
        serializer = SessionSerializer(sessions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    else:
        sessions = Session.objects.none()
        serializer = SessionSerializer(sessions)
        return Response(serializer.data, status=status.HTTP_404_NOT_FOUND)


class VideoList(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        # space = self.kwargs.get('space')
        # videos = Video.objects.filter(space=space)
        videos = Video.objects.all()
        serializer = VideoSerializer(videos, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = VideoSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class VideoDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return Video.objects.get(pk=pk)
        except Video.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        video = self.get_object(pk)
        serializer = VideoSerializer(video)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        video = self.get_object(pk)
        serializer = VideoSerializer(video, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        video = self.get_object(pk)
        video.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class VideoSubtitleFilesList(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get(self, request, format=None, **kwargs):
        # space = self.kwargs.get('space')
        # videos = Video.objects.filter(space=space)
        video_subtitle_files = VideoSubtitlesFile.objects.all()
        serializer = VideoSubtitlesFileSerializer(video_subtitle_files, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = VideoSubtitlesFileSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

class VideoSubtitlesFileDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return VideoSubtitlesFile.objects.get(pk=pk)
        except VideoSubtitlesFile.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        video_subtitles_file = self.get_object(pk)
        serializer = VideoSubtitlesFileSerializer(video_subtitles_file)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        video_subtitles_file = self.get_object(pk)
        serializer = VideoSubtitlesFileSerializer(video_subtitles_file, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        video_subtitles_file = self.get_object(pk)
        video_subtitles_file.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class AuditoriumViewSet(viewsets.ModelViewSet):
    authentication_classes = ()
    permission_classes = ()
    """
    API endpoint that allows auditoriums to be viewed or edited.
    """
    queryset = Auditorium.objects.all()
    serializer_class = AuditoriumSerializer


class VideoViewSet(viewsets.ModelViewSet):
    authentication_classes = ()
    permission_classes = ()
    """
    API endpoint that allows videos to be viewed or edited.
    """
    queryset = Video.objects.all()
    serializer_class = VideoSerializer


@api_view(['GET'])
@permission_classes([IsAuthenticated, VerifyToken])
def get_aud_dates(request, **kwargs):
    offset = request.GET.get('time_offset')
    
    if kwargs.get('space'):
        space = kwargs.get('space')
        scheduled_auds = Auditorium.objects.filter(
            space=space).exclude(start_date_time=None, end_date_time=None)
    else:
        scheduled_auds = Auditorium.objects.exclude(
            start_date_time=None, end_date_time=None)
    ordered_auds = scheduled_auds.order_by('start_date_time')
    dates = return_aud_dates(ordered_auds, offset)

    res = {
        'dates': dates
    }
    return Response(res, status=status.HTTP_200_OK)
    # except:
    #     res = {
    #         'error': 'There was a problem'
    #         }
    #     return Response(res, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([IsAuthenticated, VerifyToken])
def get_auditoriums_by_date(request,  **kwargs):
    try:
        offset = request.GET.get('time_offset')
        unformatted_date = request.data['date']
        date = datetime.date.fromisoformat(unformatted_date)
        if kwargs.get('space'):
            space = kwargs.get('space')
            scheduled_auds = Auditorium.objects.filter(
                space=space).exclude(start_date_time=None, end_date_time=None)
        else:
            scheduled_auds = Auditorium.objects.exclude(
                start_date_time=None, end_date_time=None)
        ordered_auds = scheduled_auds.order_by('start_date_time')
        data = return_day_auds(ordered_auds, date, offset)

        return Response(data, status=status.HTTP_200_OK)
    except (KeyError, ValueError):
        res = {
            'error': 'Please provide a valid date'
        }
        return Response(res, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([AllowAny,])
def get_current_date(request,  **kwargs):
    res = {
        'date': get_now_datetime()
    }
    return Response(res, status=status.HTTP_200_OK)


