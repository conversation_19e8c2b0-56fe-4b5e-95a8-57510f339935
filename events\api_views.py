from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAdmin<PERSON>ser
from rest_framework.response import Response
from rest_framework import status
from .models import Event

@api_view(['POST'])
@permission_classes([IsAdminUser])
def update_event_domain_urls(request):
    """
    Updates the domain_url for all events.
    Replaces an old environment name with a new one in the domain_url.
    Expects 'new_env_name' and 'current_env_name' in the request data.
    """
    new_env_name = request.data.get('new_env_name')
    current_env_name = request.data.get('current_env_name')

    if not new_env_name or not current_env_name:
        return Response(
            {"error": "Both 'new_env_name' and 'current_env_name' are required."},
            status=status.HTTP_400_BAD_REQUEST
        )

    if new_env_name == current_env_name:
        return Response(
            {"error": "'new_env_name' and 'current_env_name' must be different."},
            status=status.HTTP_400_BAD_REQUEST
        )

    events = Event.objects.all()
    updated_count = 0
    for event in events:
        if event.domain_url and current_env_name in event.domain_url:
            event.domain_url = event.domain_url.replace(current_env_name, new_env_name)
            event.save()
            updated_count += 1

    return Response(
        {"message": f"{updated_count} event domain_urls updated successfully."},
        status=status.HTTP_200_OK
    ) 