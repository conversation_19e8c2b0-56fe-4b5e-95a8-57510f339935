from .models import Account, Access, AccessSet
from rest_framework import serializers
from events.serializers import EventSerializer



class AccessSerializer(serializers.ModelSerializer):
    class Meta:
        model=Access
        fields = '__all__'


# class AccessField(serializers.PrimaryKeyRelatedField):
class AccessSetSerializer(serializers.ModelSerializer):
    class Meta:
        model=AccessSet
        fields = '__all__'


class AccountSerializer(serializers.ModelSerializer):
    events = EventSerializer(many=True, required=False)
    
    class Meta:
        model=Account
        fields = '__all__'
        depth=1

    def to_representation(self, instance):
        ret = super().to_representation(instance)
        if 'user' in self.context.keys():
            user = self.context['user']
            if user.is_staff:
                ret["access"] = list(Access.objects.values_list('name', flat=True))
                ret["role"] = 'ADMIN'
            else:
                try:
                    access_set = instance.access_sets.get(user=user)
                    if access_set.role == 'ADMIN':
                         ret["access"] = list(Access.objects.values_list('name', flat=True))
                    else:
                        ret["access"] = list(access_set.accesses.values_list('name', flat=True))
                    ret["role"] = access_set.role
                except Exception:
                    pass
        return ret
    
        