# Generated by Django 3.0.9 on 2021-03-04 17:07

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0017_auto_20210303_1846'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditorium',
            name='check_in',
            field=models.BooleanField(default=False, help_text='For conferences.io check in for CE credits'),
        ),
        migrations.AddField(
            model_name='auditorium',
            name='check_out',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='auditorium',
            name='conference_session_url',
            field=models.URLField(blank=True, help_text='Only for auditoriums with at least check in enabled (check out may also be enabled). Needs to be filled in if enabled', max_length=5000, null=True),
        ),
        migrations.AlterField(
            model_name='auditorium',
            name='video_call_url',
            field=models.URLField(blank=True, help_text='Only for auditoriums of type zoom. Needs to be filled in if enabled', max_length=5000, null=True),
        ),
    ]
