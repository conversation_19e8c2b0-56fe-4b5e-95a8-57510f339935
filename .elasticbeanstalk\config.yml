branch-defaults:
  dev:
    environment: mbvs-server-v2-dev2
  main:
    environment: mbvs-server-v2-staging
  production:
    environment: mbvs-server-v2-prod
    group_suffix: null
environment-defaults:
  mbvs-server-v2-dev2:
    branch: null
    repository: null
global:
  application_name: mbvs-server-v2-prod
  branch: null
  default_ec2_keyname: aws-eb-v2
  default_platform: Python 3.8 running on 64bit Amazon Linux 2
  default_region: us-east-1
  include_git_submodules: true
  instance_profile: null
  platform_name: null
  platform_version: null
  profile: eb-cli
  repository: null
  sc: git
  workspace_type: Application
