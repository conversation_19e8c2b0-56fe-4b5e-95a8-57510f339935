import io, PyPDF2, csv

from .models import CreditDefinition, CreditRecord, Criteria
from .serializers import CompletedCriteriaSerializer
from .utils import show_criteria, get_credit_definition, get_or_create_record, get_or_create_completed_criteria, get_template_data, calculate_approval
from helpers.tenants.tenant_helpers import get_current_tenant, get_tenant_from_host
from helpers.subscription.subscription_helpers import get_subscriber
from helpers.imports.subscriptions_helpers import get_subscriber as get_subscriber_email
from helpers.permissions.permissions import VerifyToken
from helpers.imports.universal_helpers import get_default_false
from expohall.utils import export_boolean, export_date_time

from django import forms
from django.views.generic import ListView
from django.contrib import messages
from django.shortcuts import redirect, render
from django.http import HttpResponse, JsonResponse, StreamingHttpResponse
from django.core.mail import EmailMessage
from django.template import Context, Template
from django.utils import timezone
from django.db.models import Q

from rest_framework import permissions, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import api_view, permission_classes
from rest_framework.views import APIView

from django_tenants.utils import schema_context
from extra_views import ModelFormSetView
from PyPDF2.generic import NameObject, NumberObject


#api sent upon completion of a criteria: except polls
#gets or creates credit record, added the criteria, checks approval
@api_view(['POST'])
@permission_classes([IsAuthenticated, VerifyToken])
def create_completed_criteria(request,  **kwargs):
    try:
        criteria_id = request.data['criteria']
    except (KeyError, ValueError):
        res = {
            'error': 'Please provide valid data to request'
        }
        return Response(res, status=status.HTTP_400_BAD_REQUEST)

    try:
        criteria = Criteria.objects.get(id=criteria_id)
        credit_definition = criteria.credit_definition
    except Exception:
        res = {
            'error': 'Error retrieving criteria or credit definition'
        }
        return Response(res, status=status.HTTP_400_BAD_REQUEST)

    user = request.user
    subscriber = get_subscriber(user)
    if not subscriber:
        res = {
            'error': 'Subscriber not found'
        }
        return Response(res, status=status.HTTP_400_BAD_REQUEST)

    try:
        credit_record = CreditRecord.objects.get(credit_definition=credit_definition, subscriber=subscriber)
    except CreditRecord.DoesNotExist:
        credit_record = CreditRecord.objects.create(credit_definition=credit_definition, subscriber=subscriber)
    except Exception:
        res = {
            'error': 'Error creating or retreiving credit record'
        }
        return Response(res, status=status.HTTP_400_BAD_REQUEST)
    serializer_data = {
        'credit_record': credit_record.id,
        'criteria': criteria.id
    }
    serializer = CompletedCriteriaSerializer(data=serializer_data)
    if serializer.is_valid():
        serializer.save()
        calculate_approval(credit_record)
        return Response(serializer.data, status=status.HTTP_201_CREATED)
    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


#dashboard for credits
class CreditDefinitionList(ListView):
    permission_classes = (permissions.IsAdminUser,)
    model = CreditDefinition
    ordering = ('created', )
    context_object_name = 'credits'
    template_name = 'credits/credit_definition_list.html'
    paginate_by = 30

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        event = get_current_tenant()
        context['event'] = event
        return context


#unused
class CreditRecordList(ListView):
    permission_classes = (permissions.IsAdminUser,)
    model = CreditRecord
    ordering = ('created', )
    context_object_name = 'records'
    template_name = 'credits/credit_record_list.html'
    paginate_by = 30

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        event = get_current_tenant()
        context['event'] = event
        return context
    
    def get_queryset(self):
        credit_def_id = self.kwargs.get('pk')
        try:
            credit_def = CreditDefinition.objects.get(id=credit_def_id)
        except Exception:
            return super().get_queryset()
        return CreditRecord.objects.filter(credit_definition=credit_def)


class CreditRecordModelForm(forms.ModelForm):

    class Meta:
        model = CreditRecord
        # fields = ['approved', 'issued', 'subscriber']
        fields = ['approved', 'issued']

    # def __init__(self, *args, **kwargs):
    #     super(CreditRecordModelForm, self).__init__(*args, **kwargs)
    #     instance = getattr(self, 'instance', None)
    #     if instance and hasattr(instance, 'subscriber'):
    #         # print(instance)
    #         self.fields['subscriber'].widget.attrs['readonly'] = True
    #     else:
    #         self.fields['subscriber'].widget.attrs['disabled'] = False
  

#dashboard for credit records
class CreditRecordEditable(ModelFormSetView):
    model = CreditRecord
    template_name = 'credits/credit_record_list_edit.html'
    form_class = CreditRecordModelForm
    # fields = '__all__'
    # fields = ['approved', 'issued', 'subscriber']
    paginate_by = 30
    # ordering = ('created', )
    # context_object_name = 'records'
    factory_kwargs = {'extra': 0}

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        credit_def_id = self.kwargs.get('pk')
        credit_def = CreditDefinition.objects.get(id=credit_def_id)
        context['definition'] = credit_def
        # event = get_current_tenant()
        # context['event'] = event
        return context
    
    def get_queryset(self):
        credit_def_id = self.kwargs.get('pk')
        try:
            credit_def = CreditDefinition.objects.get(id=credit_def_id)
        except Exception:
            return super().get_queryset()

        credit_records = CreditRecord.objects.filter(credit_definition=credit_def)
        # credit_records = CreditRecord.objects.all()
        query = self.request.GET.get('q', '')
        if query:
            # print(query)
            # queryset = credit_records
            queryset = Q(subscriber__user__first_name__icontains=query) | Q(subscriber__user__last_name__icontains=query)  | Q(subscriber__user__email__icontains=query)
            return credit_records.filter(queryset).order_by('subscriber__user__email')

        return credit_records

        # print(self.kwargs)
        # credit_def_id = self.kwargs.get('pk')
        # print('CREDIT DEF ID:', credit_def_id)
        # try:
        #     credit_def = CreditDefinition.objects.get(id=credit_def_id)
        # except Exception:
        #     return super().get_queryset()
        # # credits = CreditRecord.objects.annotate(earned_credits=get_credits_earned('credit_definition', 'completed_criteria'))
        # # return credits.filter(credit_definition=credit_def)
        # credit_records = CreditRecord.objects.filter(credit_definition=credit_def)
        # print(credit_record)
        # query = self.request.GET.get('q', '')
        # if query:
        #     print(query)
        # return credit_records


#download certificate
def download_templates(request, pk):
    record = CreditRecord.objects.get(id=pk)
    response = HttpResponse(content_type='application/pdf')
    response['Content-Disposition'] = f'attachment; filename={record.subscriber.user.first_name}_cert.pdf'

    template_data = get_template_data(record)
    file = record.credit_definition.template.file
    reader = PyPDF2.PdfFileReader(file)
    read_page = reader.getPage(0)
    writer = PyPDF2.PdfFileWriter()
    writer.insertPage(read_page)
    write_page = writer.getPage(0)
    writer.updatePageFormFieldValues(write_page, template_data)

    for j in range(0, len(write_page['/Annots'])):
        writer_annot = write_page['/Annots'][j].getObject()
        for field in template_data:
            # -----------------------------------------------------BOOYAH!
            if writer_annot.get('/T') == field:
                writer_annot.update({
                    NameObject("/Ff"): NumberObject(1)
                })

    writer.write(response) 
    return response


#main function for sending certificate
def send_certificate(record):
    try:
        definition = record.credit_definition
        message = definition.email_template
        subject = definition.email_subject
        event = get_current_tenant()

        context = {
                'email': record.subscriber.user.email,
                'first_name': record.subscriber.user.first_name,
                'subject': subject,
                'event': event.name,
                'last_name': record.subscriber.user.last_name,
                'credit_definition': definition.name
            }

        t = Template(message)
        html_message = t.render(Context(context))
        email = EmailMessage(
            subject=subject,
            body=html_message,
            from_email='<EMAIL>',
            to=[record.subscriber.user.email],
            reply_to=[definition.reply_to_email],
            )
        
        if definition.bcc_email:
            email.bcc = [definition.bcc_email]

        template_data = get_template_data(record)
        file = record.credit_definition.template.file
        reader = PyPDF2.PdfFileReader(file)
        read_page = reader.getPage(0)
        writer = PyPDF2.PdfFileWriter()
        writer.insertPage(read_page)
        write_page = writer.getPage(0)
        writer.updatePageFormFieldValues(write_page, template_data)

        for j in range(0, len(write_page['/Annots'])):
            writer_annot = write_page['/Annots'][j].getObject()
            for field in template_data:
                # -----------------------------------------------------BOOYAH!
                if writer_annot.get('/T') == field:
                    writer_annot.update({
                        NameObject("/Ff"): NumberObject(1)
                    })

        buffer = io.BytesIO()
        writer.write(buffer)
        email.attach(f'{record.subscriber.user.first_name}_cert.pdf', buffer.getvalue(), 'application/pdf' )
        email.content_subtype = "html"
        email.send(fail_silently=False)
        return True
    except Exception:
        return False


#send single certififate (sends regarless of approval)
def send_single_certificate(request, pk):
    record = CreditRecord.objects.get(id=pk)
    email_sent = send_certificate(record)
    if email_sent:
        record.last_issued_date = timezone.now()
        record.issued = True
        record.save()
        messages.info(request, f'Email sent to: {record.subscriber.user.email}')
    else:
        messages.error(request, 'Email sent failure')

    return redirect('record_list', record.credit_definition.id)


#send all certififates this will send to all approved and non issued
def send_all_certificates(request, pk):
    credit_def = CreditDefinition.objects.get(id=pk)
    records = CreditRecord.objects.filter(approved=True, issued=False, credit_definition=credit_def)
    # added_emails = []
    for record in records:
        email_sent = send_certificate(record)
        if email_sent:
            record.last_issued_date = timezone.now()
            record.issued = True
            record.save()
            messages.info(request, f'Email sent to: {record.subscriber.user.email}')
        else:
            messages.error(request, 'Email sent failure for: {record.subscriber.user.email}')
    return redirect('record_list', pk)


class Echo:
    """An object that implements just the write method of the file-like
    interface.
    """
    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


def iter_credit_record_items(records, pseudo_buffer, schema_name):
    with schema_context(schema_name):
        writer = csv.writer(pseudo_buffer)
        headers = [
            'credit_definition', 'email', 'first_name', 'last_name', 'completed_criteria', 'created', 'last_modified', 'approved', 'issued',  'last_issued_date'
        ]
        yield writer.writerow(headers)
        
        for record in records:
            email = record.subscriber.user.email
            first_name = record.subscriber.user.first_name
            last_name = record.subscriber.user.last_name
            completed_criteria = show_criteria(record.completed_criteria.all())
            created = export_date_time(record.created)
            last_modified = export_date_time(record.created)
            approved = export_boolean(record.approved)
            issued = export_boolean(record.issued)
            last_issued_date = export_date_time(record.last_issued_date)

            row = [
               record.credit_definition.name, email, first_name, last_name, completed_criteria, created, last_modified, approved, issued, last_issued_date
            ]
            yield writer.writerow(row)

            
#export records
@permission_classes([permissions.IsAdminUser,])
def export_credit_records_data(request, pk):
    event = get_tenant_from_host(request)
    if not event:
        res = {'error': 'Error parsing event'}
        return JsonResponse(res, status=400)

    with schema_context(event.schema_name):
        credit_def = CreditDefinition.objects.get(id=pk)
        records = CreditRecord.objects.filter(credit_definition=credit_def)
    
        response = StreamingHttpResponse(
            streaming_content=(iter_credit_record_items(records, Echo(), event.schema_name)),
            content_type='text/csv',
        ) 
        response['Content-Disposition'] = f'attachment; filename={credit_def.name}_credit_records.csv'
    return response


#import records
class CreditRecordUpload(APIView):
    permission_classes = (permissions.IsAdminUser,)

    def get(self, request):
        template_name = 'event_upload/import_records.html'
        return render(request, template_name)

    def post(self, request, format=None, **kwargs):
        template_name = 'event_upload/import_records.html'
        try:
            csv_file = request.FILES['eventfile']
        except Exception:
            context = {
                'message': 'No File Selected',
            }
            return render(request, template_name, context)
        file = io.TextIOWrapper(csv_file)
        if not csv_file.name.endswith('.csv'):
            messages.error(request, 'THIS IS NOT A CSV FILE')
        imported_records = []
        bad_rows = []
        added_criteria = []
        reader = csv.DictReader(file)
        for row in reader:
            try:
                credit_definition = row['credit_definition']
                email = row['email']
                completed_criteria = row['completed_criteria']
                approved = get_default_false(row['approved'])
                issued = get_default_false(row['issued'])
            except Exception:
                context = {
                    'message': 'Make sure all the headers are named correctly in your csv and try again',
                }
                return render(request, template_name, context)

            credit_def = get_credit_definition(credit_definition)
            subscriber = get_subscriber_email(email)

            if not credit_def or not subscriber:
                bad_rows.append(email)
                continue

            record = get_or_create_record(credit_def, subscriber)
            if not record:
                bad_rows.append(email)
                continue
            record.approved = approved
            record.issued = issued
            if completed_criteria != '':
                criteria_list = completed_criteria.split(' \ ')    
                added_criteria = added_criteria + get_or_create_completed_criteria(criteria_list, record)

            if record.approved == True:
                record.date_approved = timezone.now()

            record.save()
            imported_records.append(record)

        context = {
            'message': 'Upload Complete',
            'imported_records': imported_records,
            'bad_rows': bad_rows,
            'added_criteria': added_criteria
            }
        return render(request, template_name, context)
            