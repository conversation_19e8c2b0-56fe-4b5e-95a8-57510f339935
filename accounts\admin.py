from django.contrib import admin
from .models import Account, Invite, Access, AccessSet


class AccountAdmin(admin.ModelAdmin):
    autocomplete_fields =['created_by']
    search_fields =['code', 'name']
    ordering = ['code']


class InviteAdmin(admin.ModelAdmin):
    autocomplete_fields = ['sent_by', 'account', 'accesses']


class AccessAdmin(admin.ModelAdmin):
    search_fields = ['name']


class AccessSetAdmin(admin.ModelAdmin):
    autocomplete_fields = ['user', 'account', 'accesses']
    search_fields = ['user__first_name', 'user__first_name', 'user__email', 'account__code', 'account__name', 'accesses__name' ]


# Register your models here.
admin.site.register(Account, AccountAdmin)
admin.site.register(Invite, InviteAdmin)
admin.site.register(Access, AccessAdmin)
admin.site.register(AccessSet, AccessSetAdmin)
