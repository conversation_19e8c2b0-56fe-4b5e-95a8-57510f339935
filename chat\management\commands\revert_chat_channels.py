from chat.utils import create_channel_with_team
from chat.models import Chat
from events.models import Event
from spaces.models import Space
from auditorium.models import Auditorium
from expohall.models import Tile
from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
import time


class Command(BaseCommand):
    def handle(self, *args, **options):
        chats = Chat.objects.all()
        for chat in chats:
            chat.chat_channel = False
            chat.save()