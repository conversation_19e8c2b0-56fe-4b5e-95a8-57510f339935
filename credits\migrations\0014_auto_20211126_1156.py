# Generated by Django 3.2.8 on 2021-11-26 16:56

from django.db import migrations, models
import tinymce.models


class Migration(migrations.Migration):

    dependencies = [
        ('credits', '0013_alter_creditdefinition_email_template'),
    ]

    operations = [
        migrations.AddField(
            model_name='creditdefinition',
            name='min_credits',
            field=models.IntegerField(blank=True, help_text='Minimum amount of credits to be earned in the certificate. Even if no criteria completed, if issued will be given this amount. Must be less than or equal to max credits', null=True),
        ),
        migrations.AddField(
            model_name='creditrecord',
            name='date_approved',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='criteria',
            name='required',
            field=models.BooleanField(default=True, help_text='Whether required or not. If partial credits not allowed on definition this value is ignored.'),
        ),
        migrations.AlterField(
            model_name='creditdefinition',
            name='email_template',
            field=tinymce.models.HTMLField(blank=True, default=" <img src='https://i.ibb.co/5GR1s8y/Banner.png' alt='Red Banner' border='0' />\n                        <p style='color: #000000; font-size: 14px; font-weight: bold; margin-left: 10px;'>Dear {{first_name}},</p>\n                        <br />\n                        <p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>Congratulations on attending {{event}} and successfully earning the attached certificate.</p>\n                        <br />\n                        <p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>If you are having difficulty downloading the certificate, reach out to one of our team members at <a style='text-decoration: none; color: #a10c00; font-weight: bold;' href='mailto:<EMAIL>' target='_blank' rel='noopener'><EMAIL></a></p>\n                        <p style='color: #000000; font-size: 14px; margin-top: 20px; margin-left: 10px;'>Sincerely,</p>\n                        <p style='color: #a10c00; font-size: 14px; margin-top: 10px; font-weight: bold; margin-left: 10px;'>The Matchbox Team</p>\n                        <p style='color: #000000; font-size: 14px; margin-top: 20px;'><a href='https://matchboxvirtual.com/' target='_blank' rel='noreferrer noopener'><img style='width: 200px; height: 56px; margin-left: 10px;' src='https://i.ibb.co/804t9TG/01-MVM-logo.png' alt='01-MVM-logo' border='0' /></a></p>", null=True, verbose_name='Certificate Email Template'),
        ),
        migrations.AlterField(
            model_name='creditdefinition',
            name='max_credits',
            field=models.IntegerField(blank=True, help_text='Maximum amount of credits to be earned in the certificate', null=True),
        ),
    ]
