from auditorium.models import Video
from auditorium.multipart_upload import AmazonS3

from django.conf import settings

from huey.contrib.djhuey import task, db_task
from urllib.parse import urlparse


@task()
def print_task():
    print('Task')


@db_task()
def create_auditorium_video(video_file, auditorium):
    print('CREATE_AUDITORIUM_VIDEO - START')
    auditorium_video = Video.objects.create(name=video_file.name, auditorium=auditorium, video_file=video_file)


@task()
def get_aws_data(request, video_file, auditorium):
    print('GET_AWS_DATA - START')
    s3_resource = AmazonS3(aws_access_key_id=settings.AWS_ACCESS_KEY_ID, aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY, region_name=settings.AWS_S3_REGION_NAME)

    bucket_name = settings.AWS_STORAGE_BUCKET_NAME
    get_url = request.META.get('HTTP_REFERER')
    get_sub_dom = urlparse(get_url)
    sub_domain = get_sub_dom.hostname.split('.')[0]
    file_bucket_location = f'media/{sub_domain}/auditorium_videos/{video_file.name}'

    s3_resource.push_data_to_s3_bucket(bucket_name, video_file, file_bucket_location, video_file.size, video_file.content_type)

    print('GET_AWS_DATA - END')
    print('CREATE_AUDITORIUM_VIDEO - CALL')
    create_auditorium_video(video_file, auditorium)

    # create_auditorium_video(video_file, auditorium)
    # auditorium_video = Video.objects.create(name=video_file.name, auditorium=auditorium, video_file=video_file)




