# Generated by Django 3.0.9 on 2021-04-12 20:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('subscriptions', '0004_auto_20210111_1935'),
        ('auditorium', '0019_auto_20210308_1556'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditorium',
            name='whitelist',
            field=models.ManyToManyField(blank=True, help_text='if an attendee is added to this field the tile is considered whitelisted and will only show up for attendees who are on the whitelist', related_name='whitelisted_auditoriums', to='subscriptions.Subscription'),
        ),
    ]
