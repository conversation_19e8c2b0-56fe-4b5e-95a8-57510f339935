# Generated by Django 3.0.9 on 2021-01-04 20:56

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('spaces', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Auditorium',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, null=True, unique=True)),
                ('start_date_time', models.DateTimeField(null=True)),
                ('end_date_time', models.DateTimeField(null=True)),
                ('space', models.ManyToManyField(to='spaces.Space')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='Video',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('file_source', models.FileField(upload_to='auditorium_videos/')),
                ('auditorium', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='videos', to='auditorium.Auditorium')),
                ('space', models.ManyToManyField(to='spaces.Space')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
