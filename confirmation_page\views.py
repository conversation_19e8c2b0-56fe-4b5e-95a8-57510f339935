from django.shortcuts import render
from django.http import Http404

from .models import ConfirmationPage
from .serializers import ConfirmationPageSerializer
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status, permissions
from rest_framework.permissions import IsAuthenticated
from helpers.permissions.permissions import VerifyToken

# Create your views here.


class ConfirmationPageAPI(APIView):
    permission_classes = (permissions.AllowAny,)

    def get(self, request, format=None, **kwargs):
        pages = ConfirmationPage.objects.all()
        if pages:
            page = ConfirmationPage.objects.all().order_by('id').last()
            serializer = ConfirmationPageSerializer(page)
        else:
            serializer = ConfirmationPageSerializer(pages, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, format=None, **kwargs):
        serializer = ConfirmationPageSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)



class ConfirmationPageDetail(APIView):
    permission_classes = (IsAuthenticated, VerifyToken)

    def get_object(self, pk, **kwargs):
        try:
            return ConfirmationPage.objects.get(pk=pk)
        except ConfirmationPage.DoesNotExist as e:
            raise Http404 from e

    def get(self, request, pk, format=None, **kwargs):
        page = self.get_object(pk)
        serializer = ConfirmationPageSerializer(page)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def put(self, request, pk, format=None, **kwargs):
        page = self.get_object(pk)
        serializer = ConfirmationPageSerializer(page, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def delete(self, request, pk, format=None, **kwargs):
        page = self.get_object(pk)
        page.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)