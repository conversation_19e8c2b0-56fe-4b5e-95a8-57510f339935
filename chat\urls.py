from django.urls import path, include
from . import views
urlpatterns = [
    path('chats/', include(([
        path('', views.ChatList.as_view()),
        path('<pk>/', views.ChatDetail.as_view()),
        path('<str:chat>/messages/', views.MessageListByChat.as_view()),
        path('<str:chat>/messages/<pk>', views.MessageDetailByChat.as_view()),
        ], 'chat'), namespace='chats')),
    path('messages/', include(([
        path('', views.MessageList.as_view()),
        path('<pk>/', views.MessageDetail.as_view()),
        ], 'chat'), namespace='messages')),
    # path('private_chats/', include(([
    # path('', views.PrivateChatList.as_view()),
    # path('<pk>/', views.PrivateChatDetail.as_view()),
    # ], 'private_chats'), namespace='private_chats')),
    path('chat_export/', views.ChatExportList.as_view(), name='chat_export_list'),
    # path('chat_export2/', views.ChatExportList2.as_view(), name='chat_export_list_2')

]