from django.contrib import admin
from .models import Auditorium, Video, AuditoriumResource, Session, VideoSubtitlesFile


class AuditoriumAdmin(admin.ModelAdmin):
    search_fields = ('name',)
    list_display = (
        'name',  
        'aud_type',
        'start_date_time',
        'end_date_time',
        'is_on_demand',
        'chat_toggle',
        'order',
        'video_call_url',
        'id'
    )
    list_filter = ('space', )
    filter_horizontal = ['space', 'allow_list', 'related_rooms', ]
    fieldsets = (
        (None, {
            'fields': ('name', 'space', 'aud_type', 'start_date_time', 'end_date_time', 'order')
        }),
        ('Details', {
            'fields': ('image', 'image_alt_text', 'html_blurb', 'related_rooms', 'related_rooms_name'),
        }),

        ('Type Video Settings', {
            'classes': ('collapse',),
            'fields': ('is_on_demand', 'chat_toggle'),
        }),
        ('Type Zoom Settings', {
            'classes': ('collapse',),
            'fields': ('video_call_url',),
        }),
        ('Allow List', {
            'classes': ('collapse',),
            'fields': ('allow_list',),
        }),
    )
    save_as = True


class VideoAdmin(admin.ModelAdmin):
    readonly_fields = ('mux_asset_id', 'mux_playback_id', 'mux_preview_url')
    search_fields = ('name', 'auditorium__name',)
    list_display = ('name',
                    'duration',
                    'video_url',
                    'auditorium',
                    'order',
                    'cc_enabled',
                    'is_live_stream')
    list_filter = ('auditorium', )
    fieldsets = (
        (None, {
            'fields': ('name', 'auditorium', 'video_url', 'duration', 'order')
        }),

        ('Settings', {
            # 'classes': ('collapse',),
            'fields': ('cc_enabled', 'is_live_stream',),
        }),
        ('Mux Settings', {
            'classes': ('collapse',),
            'fields': ('video_file', 'mux_asset_id', 'mux_playback_id', 'mux_preview_url'),
        }),
    )
    save_as = True


class VideoSubtitlesFileAdmin(admin.ModelAdmin):
    readonly_fields = ('mux_track_id',)
    list_display = ('ref',
                    'video',
                    'subtitles_file', 
                    'language_code', 
                    'cc_enabled')
    save_as = True

class SessionAdmin(admin.ModelAdmin):
    list_display = ('auditorium',
                    'start_time',
                    'pre_roll',
                    'post_roll')
    list_filter = ('auditorium', )
    save_as = True
    exclude = ['end_time', ]


class AuditoriumResourceAdmin(admin.ModelAdmin):
    search_fields = ('name', 'auditorium__name')
    list_display = ('auditorium',
                    'tab_type',
                    'name',
                    'sub_header',
                    'priority')
    list_filter = ('auditorium', )
    fieldsets = (
        (None, {
            'fields': ('tab_type', 'name', 'sub_header', 'auditorium', 'priority')
        }),

        ('Type PDF Settings', {
            'fields': ('pdf_file',),
        }),
        ('Type Website Settings', {
            'fields': ('url',),
        }),
    )
    save_as = True


admin.site.register(Auditorium, AuditoriumAdmin)
admin.site.register(Video, VideoAdmin)
admin.site.register(VideoSubtitlesFile, VideoSubtitlesFileAdmin)
admin.site.register(AuditoriumResource, AuditoriumResourceAdmin)
admin.site.register(Session, SessionAdmin)
