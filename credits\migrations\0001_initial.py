# Generated by Django 3.2.8 on 2021-11-10 20:24

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('subscriptions', '0010_subscription_chat_token'),
    ]

    operations = [
        migrations.CreateModel(
            name='CreditDefinition',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='For internal use only', max_length=100, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('allow_partial_credits', models.BooleanField(default=False)),
                ('max_credits', models.IntegerField(blank=True, null=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['created'],
            },
        ),
        migrations.CreateModel(
            name='CreditTemplate',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(help_text='For internal use only', max_length=100, unique=True)),
                ('file', models.FileField(upload_to='credit_templates')),
                ('created', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['created'],
            },
        ),
        migrations.CreateModel(
            name='Criteria',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text='For internal use only', max_length=100, unique=True)),
                ('credit_value', models.IntegerField()),
                ('active', models.BooleanField(default=True)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('credit_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='criteria', to='credits.creditdefinition')),
            ],
            options={
                'ordering': ['created'],
            },
        ),
        migrations.CreateModel(
            name='WatchSessionCriteria',
            fields=[
                ('criteria_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='credits.criteria')),
                ('attendance', models.BooleanField(default=False)),
            ],
            bases=('credits.criteria',),
        ),
        migrations.CreateModel(
            name='CreditRecord',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('last_modified', models.DateTimeField(auto_now=True)),
                ('credit_definition', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_records', to='credits.creditdefinition')),
                ('subscriber', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_records', to='subscriptions.subscription')),
            ],
            options={
                'ordering': ['created'],
                'unique_together': {('subscriber', 'credit_definition')},
            },
        ),
        migrations.AddField(
            model_name='creditdefinition',
            name='template',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_definitions', to='credits.credittemplate'),
        ),
        migrations.CreateModel(
            name='CompletedCriteria',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created', models.DateTimeField(auto_now_add=True)),
                ('credit_record', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='completed_criteria', to='credits.creditrecord')),
                ('criteria', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='credits.criteria')),
            ],
            options={
                'ordering': ['created'],
            },
        ),
    ]
