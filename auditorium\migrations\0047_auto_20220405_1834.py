# Generated by Django 3.2.12 on 2022-04-05 22:34

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0046_videosubtitlesfile'),
    ]

    operations = [
        migrations.AddField(
            model_name='videosubtitlesfile',
            name='cc_enabled',
            field=models.BooleanField(default=False),
        ),
        migrations.AlterField(
            model_name='videosubtitlesfile',
            name='language_code',
            field=models.CharField(choices=[('en', 'en'), ('es', 'es'), ('ca', 'ca'), ('fr', 'fr'), ('de', 'de'), ('ru', 'ru'), ('it', 'it'), ('da', 'da'), ('pt', 'pt'), ('ch', 'ch'), ('zh', 'zh'), ('nl', 'nl'), ('no', 'no'), ('uk', 'uk'), ('ja', 'ja'), ('ko', 'ko'), ('hr', 'hr'), ('fi', 'fi'), ('sv', 'sv'), ('pl', 'pl'), ('el', 'el'), ('hu', 'hu'), ('tr', 'tr'), ('cs', 'cs'), ('et', 'et'), ('di', 'di')], default='en', max_length=5, verbose_name='language'),
        ),
    ]
