# Generated by Django 3.0.9 on 2021-09-21 19:24

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('events', '0026_auto_20210917_1310'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('chat', '0006_auto_20210921_1332'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrivateChat',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(blank=True, max_length=200, null=True)),
                ('chat_channel', models.BooleanField(default=False)),
                ('chat_channel_id', models.CharField(blank=True, max_length=200, null=True)),
                ('event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='events.Event')),
                ('participants', models.ManyToManyField(blank=True, related_name='chats', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
