from .models import Auditorium, Video, Session
from helpers.tenants.tenant_helpers import get_current_tenant
from django import forms
from django.db.models import Q
from django.views.generic import ListView, UpdateView, CreateView, DetailView
from django.contrib.messages.views import SuccessMessageMixin
from rest_framework import permissions
from tinymce.widgets import TinyMCE


class AuditoriumList(ListView):
    permission_classes = (permissions.IsAdminUser,)
    model = Auditorium
    ordering = ('name', )
    context_object_name = 'auditoriums'
    template_name = 'auditoriums/auditorium_list.html'
    paginate_by = 30

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        event = get_current_tenant()
        context['event'] = event
        return context

    def get_queryset(self):
        auditoriums = Auditorium.objects.all()
        query = self.request.GET.get('q', '')

        if query:
            queryset = Q(name__icontains=query)
            return auditoriums.filter(queryset).order_by('name')
        return auditoriums.order_by('name')


class AuditoriumModelForm(forms.ModelForm):

    class Meta:
        model = Auditorium
        fields = (
            'name', 'aud_type', 'start_date_time', 'end_date_time', 'image', 'image_alt_text', 'order', 'video_call_url',
            'conference_session_url', 'html_blurb', 'is_on_demand', 'chat_toggle',
            'check_in', 'check_out', 'related_rooms', 'time_created', 'allow_list', 
        )
        html_blurb = forms.CharField(widget=TinyMCE(attrs={'width': '400px'}))
        time_created = forms.DateTimeField(required=False, widget=forms.TextInput(attrs={'readonly': 'readonly'}))


class VideoModelForm(forms.ModelForm):

    class Meta:
        model = Video
        fields = (
            'name', 'video_url', 'duration', 'cc_enabled', 'is_live_stream', 'order', 'time_created', 
        )
        time_created = forms.DateTimeField(required=False, widget=forms.TextInput(attrs={'readonly': 'readonly'}))

        


class SessionModelForm(forms.ModelForm):

    class Meta:
        model = Session
        fields = (
            'start_time', 'pre_roll', 'post_roll', 'time_created', 
        )
        time_created = forms.DateTimeField(required=False, widget=forms.TextInput(attrs={'readonly': 'readonly'}))


class AuditoriumDetailView(SuccessMessageMixin, DetailView):
    model = Auditorium
    template_name = 'auditoriums/auditorium_details.html'
    context_object_name = 'auditorium'


class AuditoriumModifyView(SuccessMessageMixin, UpdateView):
    model = Auditorium
    form_class = AuditoriumModelForm
    template_name = 'auditoriums/auditorium_change_form.html'
    context_object_name = 'auditorium'


class AuditoriumCreateView(SuccessMessageMixin, CreateView):
    model = Auditorium
    form_class = AuditoriumModelForm
    template_name = 'auditoriums/auditorium_create_form.html'
    context_object_name = 'auditorium'


class SessionModifyView(SuccessMessageMixin, UpdateView):
    model = Session
    form_class = SessionModelForm
    template_name = 'auditoriums/session_change_form.html'
    context_object_name = 'session'


class SessionCreateView(SuccessMessageMixin, CreateView):
    model = Session
    form_class = SessionModelForm
    template_name = 'auditoriums/session_create_form.html'
    context_object_name = 'session'


class VideoModifyView(SuccessMessageMixin, UpdateView):
    model = Video
    form_class = VideoModelForm
    template_name = 'auditoriums/video_change_form.html'
    context_object_name = 'video'


class VideoCreateView(SuccessMessageMixin, CreateView):
    model = Video
    form_class = VideoModelForm
    template_name = 'auditoriums/video_create_form.html'
    context_object_name = 'video'

    # def post(self, request, **kwargs):
    #     self.object = self.get_object()
    #     if 'cover-form' in request.POST:
    #         try:
    #             if self.object.cover_data:
    #                 cover_form = TileCoverModelForm(
    #                     request.POST, instance=self.object.cover_data)
    #         except:
    #             cover_form = TileCoverModelForm(request.POST)

    #         if cover_form.is_valid():
    #             cover = cover_form.save(commit=False)
    #             cover.tile = self.object
    #             cover.save()
    #             messages.add_message(self.request, messages.SUCCESS,
    #                                  'Update Cover successful: ' + format_datetime(timezone.now()))
    #     else:
    #         form = TileModelForm(
    #             request.POST, request.FILES, instance=self.object)
    #         if form.is_valid():
    #             form.save()
    #             messages.add_message(self.request, messages.SUCCESS,
    #                                  'Update successful: ' + format_datetime(timezone.now()))
    #     return redirect('tile_modify_view', pk=self.object.id)

    # def get_context_data(self, **kwargs):
    #     context = super().get_context_data(**kwargs)
    #     context['tile_form'] = context['form']
    #     try:
    #         if self.object.cover_data:
    #             context['tile_cover_form'] = TileCoverModelForm(
    #                 instance=self.object.cover_data)
    #     except:
    #         context['tile_cover_form'] = TileCoverModelForm()

    #     return context
# create auditorium
# modify auditorium
# add session?
# formset with adding videos
