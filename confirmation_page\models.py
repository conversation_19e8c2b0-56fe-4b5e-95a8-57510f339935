from django.db import models
from tinymce import models as tinymce_models

# Create your models here.
class ConfirmationPage(models.Model):
   logo = models.ImageField(upload_to='confirmation_logos')
   background_image = models.ImageField(upload_to='confirmation_background',blank=True, null=True)
   background_color = models.CharField(max_length=9,blank=True, null=True )
   confirmation_header = models.CharField(max_length=250, blank=True, null=True)
   confirmation_text = models.TextField(blank=True, null=True)
   link_text = models.CharField(max_length=100, blank=True, null=True )
   confirmation_email_subject = models.CharField(max_length=500, default='Event Registration Confirmation')
   confirmation_email_text = models.TextField( blank=True, null=True)
   confirmation_email_signoff = models.CharField(max_length=500, default='The Virtual Spaces Team')
   html_blurb = tinymce_models.HTMLField(blank=True, null=True, verbose_name="Confirmation Page Html", help_text="Please use this editor to change text in the tile confirmation page. If you use this editor the old description or blurb will be repalced.")
   objects = models.Manager()

#    def __str__(self):
#        if 
#        return f'{self.confirmation_header}'


    