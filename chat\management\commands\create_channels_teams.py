from chat.utils import create_channel_with_team
from chat.models import Chat
from events.models import Event
from spaces.models import Space
from auditorium.models import Auditorium
from expohall.models import Tile
from django.core.management.base import BaseCommand
from django_tenants.utils import schema_context
import time


class Command(BaseCommand):
    def handle(self, *args, **options):
        events = Event.objects.all()
        for event in events:
            if event.name == 'public':
                continue
            
            elif event.frontend_sub_domain != event.team_name:
                event.team_name = event.frontend_sub_domain
                event.save()
                with schema_context(event.schema_name):
                    auditoriums = Auditorium.objects.all()
                    tiles = Tile.objects.all()
                    spaces = Space.objects.all()
                    
                    aud_chats = Chat.objects.filter(auditoriums__in=auditoriums)
                    tile_chats = Chat.objects.filter(tile__in=tiles)
                    space_chats = Chat.objects.filter(space__in=spaces)
                    chats = aud_chats| tile_chats | space_chats
                    for chat in chats:
                        # if not chat.chat_channel:
                        create_channel_with_team(chat, event)
                        time.sleep(0.2)


        