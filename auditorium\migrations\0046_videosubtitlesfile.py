# Generated by Django 3.2.12 on 2022-04-05 21:20

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0045_alter_video_duration'),
    ]

    operations = [
        migrations.CreateModel(
            name='VideoSubtitlesFile',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('subtitles_file', models.FileField(blank=True, null=True, upload_to='auditorium_video_subtitles')),
                ('language_code', models.CharField(choices=[('en', 'en'), ('es', 'es'), ('ca', 'ca'), ('fr', 'fr'), ('de', 'de'), ('ru', 'ru'), ('it', 'it'), ('da', 'da'), ('pt', 'pt'), ('ch', 'ch'), ('zh', 'zh'), ('nl', 'nl'), ('no', 'no'), ('uk', 'uk'), ('ja', 'ja'), ('ko', 'ko'), ('hr', 'hr'), ('fi', 'fi'), ('sv', 'sv'), ('pl', 'pl'), ('el', 'el'), ('hu', 'hu'), ('tr', 'tr'), ('cs', 'cs'), ('et', 'et'), ('di', 'di')], default='en', max_length=5)),
                ('ref', models.CharField(blank=True, max_length=250, null=True)),
                ('video', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='video_subtitles_file', to='auditorium.video')),
            ],
        ),
    ]
