from expohall.utils import export_boolean, export_date_time, show_list
from auditorium.models import Auditorium, Session, Video


from helpers.tenants.tenant_helpers import get_tenant_from_host
from django_tenants.utils import schema_context
from django.http import JsonResponse, StreamingHttpResponse
from django.contrib.auth import get_user_model
from rest_framework.decorators import permission_classes
from rest_framework import permissions
import csv


User = get_user_model()


class Echo:
    """An object that implements just the write method of the file-like
    interface.
    """
    def write(self, value):
        """Write the value by returning it, instead of storing in a buffer."""
        return value


def iter_auditoriums(auditoriums, pseudo_buffer, schema_name):
    with schema_context(schema_name):
        writer = csv.writer(pseudo_buffer)
        headers = [
            'space_names', 'aud_name', 'aud_type', 'is_on_demand', 'chat_toggle', 'start_date_time', 'end_date_time', 'image_alt_text', 'html_blurb', 'video_call_url', 'order', 'related_rooms_name', 'related_rooms'
        ]
        yield writer.writerow(headers)
        
        for auditorium in auditoriums:
            spaces = auditorium.space.all()
            
            is_on_demand = export_boolean(auditorium.is_on_demand)
            chat_toggle = export_boolean(auditorium.chat_toggle)
            
            start_date_time = export_date_time(auditorium.start_date_time)
            end_date_time = export_date_time(auditorium.end_date_time)

            row = [
                show_list(spaces), auditorium.name, auditorium.aud_type, is_on_demand, chat_toggle, start_date_time, end_date_time, auditorium.image_alt_text, auditorium.html_blurb, auditorium.video_call_url, auditorium.order, auditorium.related_rooms_name, show_list(auditorium.related_rooms.all())
            ]
            yield writer.writerow(row)
            
@permission_classes([permissions.IsAdminUser,])
def export_auditorium_data(request):
    event = get_tenant_from_host(request)
    if not event:
        res = {'error': 'Error parsing event'}
        return JsonResponse(res, status=400)
    with schema_context(event.schema_name):
        auditoriums = Auditorium.objects.all()
    
        response = StreamingHttpResponse(
            streaming_content=(iter_auditoriums(auditoriums, Echo(), event.schema_name)),
            content_type='text/csv',
        ) 
        response['Content-Disposition'] = 'attachment; filename=auditorium_data.csv'
    return response


def iter_auditorium_videos(aud_videos, pseudo_buffer, schema_name):
    with schema_context(schema_name):
        writer = csv.writer(pseudo_buffer)
        headers = [
            'aud_name', 'vid_name', 'video_url', 'duration', 'cc_enabled', 'live_stream', 'order'
        ]
        yield writer.writerow(headers)
        
        for aud_video in aud_videos:
            cc_enabled = export_boolean(aud_video.cc_enabled)
            live_stream = export_boolean(aud_video.is_live_stream)

            row = [
                 aud_video.auditorium.name, aud_video.name, aud_video.video_url, aud_video.duration, cc_enabled, live_stream, aud_video.order
            ]
            yield writer.writerow(row)
            
@permission_classes([permissions.IsAdminUser,])
def export_auditorium_video_data(request):
    event = get_tenant_from_host(request)
    if not event:
        res = {'error': 'Error parsing event'}
        return JsonResponse(res, status=400)
    with schema_context(event.schema_name):
        aud_videos = Video.objects.all()
    
        response = StreamingHttpResponse(
            streaming_content=(iter_auditorium_videos(aud_videos, Echo(), event.schema_name)),
            content_type='text/csv',
        ) 
        response['Content-Disposition'] = 'attachment; filename=auditorium_video_data.csv'
    return response
    

def iter_auditorium_sessions(aud_sessions, pseudo_buffer, schema_name):
    with schema_context(schema_name):
        writer = csv.writer(pseudo_buffer)
        headers = [
            'aud_name', 'start_date_time', 'pre_roll_url', 'post_roll_url'
        ]
        yield writer.writerow(headers)
        
        for session in aud_sessions:
            row = [
                session.auditorium.name, session.start_time, session.pre_roll, session.post_roll
            ]
            yield writer.writerow(row)
            
@permission_classes([permissions.IsAdminUser,])
def export_auditorium_session_data(request):
    event = get_tenant_from_host(request)
    if not event:
        res = {'error': 'Error parsing event'}
        return JsonResponse(res, status=400)
    with schema_context(event.schema_name):
        aud_sessions = Session.objects.all()
    
        response = StreamingHttpResponse(
            streaming_content=(iter_auditorium_sessions(aud_sessions, Echo(), event.schema_name)),
            content_type='text/csv',
        ) 
        response['Content-Disposition'] = 'attachment; filename=auditorium_session_data.csv'
    return response
