container_commands:
  collectstatic:
    command: "source /var/app/venv/*/bin/activate && python manage.py collectstatic --no-input"

  01_migrate:
    command: "source /var/app/venv/*/bin/activate && python manage.py migrate_schemas"
    leader_only: true

  02_createsu:
    command: "source /var/app/venv/*/bin/activate && python manage.py createsu"
    leader_only: true
    
  03_migratetendom:
    command: "source /var/app/venv/*/bin/activate && python manage.py migratetendom"
    leader_only: true
  
  04_createten:
    command: "source /var/app/venv/*/bin/activate && python manage.py createawsten"
    leader_only: true

  05_wsgipass:
    command: 'echo "WSGIPassAuthorization On" >> ../wsgi.conf'
  
option_settings:
  aws:elasticbeanstalk:application:environment:
    DJANGO_SETTINGS_MODULE: backend.settings