import uuid 

from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.db import models
# from events.models import Event
# from tenant_schemas.utils import get_tenant_model

User = get_user_model()
# Event = get_tenant_model

class Chat(models.Model):
    id = models.UUIDField(primary_key = True, default = uuid.uuid4, editable = False)
    name= models.CharField(max_length=200, blank=True, null=True)
    header_message = models.TextField(blank=True, null=True)
    # participants = models.ManyToManyField(User, related_name='chats', blank=True)
    chat_channel = models.BooleanField(default=False)
    objects=models.Manager()

    def __str__(self):
        return self.name
    
def validate_content_message(content):
    if content == "" or content is None or content.isspace():
        raise ValidationError(
            'Content is empty or invalid',
            code = 'invalid',
            params = {
                'content' : content
            }
        )

class Message(models.Model):
    id = models.UUIDField(primary_key = True, default = uuid.uuid4, editable = False)
    author = models.ForeignKey(
        User, related_name='author_messages', on_delete=models.CASCADE)
    content = models.TextField(validators=[validate_content_message])
    chat = models.ForeignKey(
        Chat, on_delete=models.CASCADE, blank=True, null=True, related_name='messages')
    timestamp = models.DateTimeField(auto_now_add=True)
    deleted = models.BooleanField(default=False)
    objects=models.Manager()

    class Meta:
        ordering = ['timestamp']

    def __str__(self):
        return self.author.first_name

# class PrivateChat(models.Model):
#     id = models.UUIDField(primary_key = True, default = uuid.uuid4, editable = False)
#     name = models.CharField(max_length=200, blank=True, null=True)
#     participants = models.ManyToManyField(User, related_name='chats', blank=True)
#     chat_channel = models.BooleanField(default=False)
#     chat_channel_id = models.CharField(max_length=200, blank=True, null=True, unique=True)
#     event = models.ForeignKey(Event, on_delete=models.CASCADE, blank=True, null=True)
#     objects=models.Manager()

#     def __str__(self):
#         return self.name