# Generated by Django 3.0.9 on 2021-02-16 15:11

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('spaces', '0005_auto_20210112_1831'),
        ('auditorium', '0005_auto_20210212_1955'),
    ]

    operations = [
        migrations.AddField(
            model_name='auditorium',
            name='is_on_demand',
            field=models.BooleanField(default=False),
        ),
        migrations.CreateModel(
            name='AuditoriumResource',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('tab_type', models.CharField(choices=[('PDF', 'PDF'), ('URL', 'Website'), ('TEXT', 'Text')], default='PDF', max_length=5)),
                ('name', models.CharField(blank=True, max_length=100, null=True)),
                ('sub_header', models.CharField(blank=True, max_length=100, null=True)),
                ('pdf_file', models.FileField(blank=True, null=True, upload_to='aud_resource_files')),
                ('image', models.ImageField(blank=True, null=True, upload_to='aud_resource_files')),
                ('url', models.URLField(blank=True, null=True)),
                ('priority', models.IntegerField(blank=True, null=True)),
                ('text', models.TextField(blank=True, null=True)),
                ('time_created', models.DateTimeField(default=django.utils.timezone.now)),
                ('auditorium', models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='resources', to='auditorium.Auditorium')),
                ('space', models.ManyToManyField(blank=True, to='spaces.Space')),
            ],
            options={
                'abstract': False,
            },
        ),
    ]
