# Generated by Django 3.2.8 on 2021-11-11 18:52

import credits.models
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0039_auditorium_related_rooms_name'),
        ('credits', '0004_auto_20211111_1256'),
    ]

    operations = [
        migrations.CreateModel(
            name='AttendSessionCriteria',
            fields=[
                ('criteria_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='credits.criteria')),
                ('time_1', models.DateTimeField(blank=True, help_text='optionally add time span that this is active for', null=True)),
                ('time_2', models.DateTimeField(blank=True, help_text='optionally add time span that this is active for', null=True)),
            ],
            bases=('credits.criteria',),
        ),
        migrations.RemoveField(
            model_name='watchsessioncriteria',
            name='attendance',
        ),
        migrations.AddField(
            model_name='watchsessioncriteria',
            name='percentage',
            field=models.DecimalField(decimal_places=2, default=0, help_text='percentage of video to be watched', max_digits=5, validators=[credits.models.validate_percent]),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='watchsessioncriteria',
            name='time_1',
            field=models.DateTimeField(blank=True, help_text='optionally add time span that this is active for', null=True),
        ),
        migrations.AddField(
            model_name='watchsessioncriteria',
            name='time_2',
            field=models.DateTimeField(blank=True, help_text='optionally add time span that this is active for', null=True),
        ),
        migrations.AlterField(
            model_name='criteria',
            name='auditorium',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='criteria', to='auditorium.auditorium'),
        ),
    ]
