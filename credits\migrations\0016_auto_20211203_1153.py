# Generated by Django 3.2.8 on 2021-12-03 16:53

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('polls', '0004_auto_20211203_1128'),
        ('credits', '0015_creditrecord_last_criteria_added_date'),
    ]

    operations = [
        migrations.AlterField(
            model_name='criteria',
            name='criteria_type',
            field=models.IntegerField(choices=[(1, 'None'), (2, 'Attendance'), (3, 'Watch'), (4, 'POLL')], default=1),
        ),
        migrations.CreateModel(
            name='PollCriteria',
            fields=[
                ('criteria_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to='credits.criteria')),
                ('poll', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='polls.poll')),
            ],
            bases=('credits.criteria',),
        ),
    ]
