from .models import Chat, Message
from rest_framework import serializers

class DisplayNameListing<PERSON>ield(serializers.Field):
    def to_representation(self, value):
        if value.screen_name:
            return value.screen_name
        else:
            return value.first_name + ' ' + value.last_name

class FirstNameListingField(serializers.Field):
    def to_representation(self, value):
        return value.first_name

class LastNameListingField(serializers.Field):
    def to_representation(self, value):
        return value.last_name

class EmailListingField(serializers.Field):
    def to_representation(self, value):
        return value.first_name

class AvatarListingField(serializers.Field):
    def to_representation(self, value):
        if not value.avatar:
            return None
        if value.avatar_schema:
            url = value.avatar.url
            split_string = url.split('/')
            split_string[4] = value.avatar_schema
            return '/'.join(split_string)
        return value.avatar.url



class MessageSerializer(serializers.ModelSerializer):
    first_name = FirstNameListingField(read_only=True, source='author')
    last_name = LastNameListingField(read_only=True, source='author')
    email= EmailListingField(read_only=True, source='author')
    avatar = AvatarListingField(read_only=True, source='author')
    display_name = DisplayNameListingField(read_only=True, source='author')
    
    class Meta:
        model = Message
        fields = "__all__"

class ChatSerializer(serializers.ModelSerializer):
    messages = MessageSerializer(many=True, required=False)
    class Meta:
        model = Chat
        fields = "__all__"

# class PrivateChatSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = PrivateChat
#         fields = "__all__"