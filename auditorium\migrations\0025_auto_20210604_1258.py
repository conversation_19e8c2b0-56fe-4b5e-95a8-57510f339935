# Generated by Django 3.0.9 on 2021-06-04 16:58

from django.db import migrations
import tinymce.models


class Migration(migrations.Migration):

    dependencies = [
        ('auditorium', '0024_auto_20210603_1456'),
    ]

    operations = [
        migrations.AddField(
            model_name='session',
            name='html_blurb',
            field=tinymce.models.HTMLField(blank=True, help_text='Please use this editor to change text in the Auditorium session. If you use this editor the old description or blurb will be repalced.', null=True, verbose_name='Auditorium Session Text'),
        ),
        migrations.AlterField(
            model_name='auditorium',
            name='html_blurb',
            field=tinymce.models.HTMLField(blank=True, help_text='Please use this editor to change text in the auditorium session. If you use this editor the old description or blurb will be repalced.', null=True, verbose_name='Auditorium Modal Description'),
        ),
        migrations.AlterField(
            model_name='auditoriumresource',
            name='html_blurb',
            field=tinymce.models.HTMLField(blank=True, help_text='Please use this editor to change text in the Auditorium resource. If you use this editor the old description or blurb will be repalced.', null=True, verbose_name='Auditorium Resource HTML'),
        ),
    ]
