# Generated by Django 3.2.8 on 2022-01-19 15:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Account',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Code to represent account, must be unique', max_length=300, null=True, unique=True)),
                ('name', models.Char<PERSON>ield(help_text='Human readable name of account', max_length=300, null=True)),
                ('time_created', models.DateTimeField(default=django.utils.timezone.now)),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_accounts', to=settings.AUTH_USER_MODEL)),
                ('users', models.ManyToManyField(related_name='accounts', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
