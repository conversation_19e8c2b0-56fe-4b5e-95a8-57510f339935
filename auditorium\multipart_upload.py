import sys
import os
import boto3
from boto3.s3.transfer import TransferConfig
import threading

class AmazonS3:

    def __init__(self, aws_access_key_id, aws_secret_access_key, region_name):
        self.s3 = boto3.resource(
            's3',
            aws_access_key_id = aws_access_key_id,
            aws_secret_access_key = aws_secret_access_key,
            region_name = region_name
        )


    def push_data_to_s3_bucket(self, bucket_name, data, file_name, file_size, content_type):   
        config = TransferConfig( 
            multipart_threshold=1024 * 25, #limit above which multiparts activate
            max_concurrency=10, #threads
            multipart_chunksize=1024 * 25, #size of data in each thread
            use_threads=True #enabling threads
        )
   
        self.s3.Object(bucket_name, file_name).upload_fileobj(
            data,
            ExtraArgs={'ContentType': content_type},
            Config=config,
            Callback=self.ProgressPercentage(data, file_size)
        )
    

    def show_contents_s3_bucket(self, bucket_name):
        bucket = self.s3.Bucket(bucket_name)
        print()
        print(f"Bucket : {bucket_name}")
        for obj in bucket.objects.all():
            print(f'filename : {obj.key} ')


    def delete_contents_s3_bucket(self,bucket_name,file_name ):
        self.s3.Object(bucket_name, file_name).delete()
        self.show_contents_s3_bucket(bucket_name)


    def empty_bucket(self, bucket_name):
        self.s3.Bucket(bucket_name).objects.all().delete()

    class ProgressPercentage(object):
            def __init__(self, filename, size):
                self._filename = filename
                self._size = float(size)
                self._seen_so_far = 0
                self._lock = threading.Lock()

            def __call__(self, bytes_amount):
                with self._lock:
                    self._seen_so_far += bytes_amount
                    percentage = (self._seen_so_far / self._size) * 100
                    sys.stdout.write(
                        "\r%s  %s / %s  (%.2f%%)" % (
                            self._filename, self._seen_so_far, self._size,
                            percentage))
                    sys.stdout.flush()



##########

# from django.conf import settings

# from boto3.s3.transfer import TransferConfig

# import boto3


# s3_resource = boto3.resource('s3')

# config = TransferConfig(
#     multipart_threshold=1024 * 25,
#     max_concurrency=10,
#     multipart_chunksize=1024 * 25,
#     use_threads=True
# )

# bucket_name = settings.AWS_STORAGE_BUCKET_NAME


# def multipart_upload_boto3(video_file):
#     file_path = os.path.dirname(__file__) + '/multipart_upload_example.pdf'
#     key = 'multipart-test/multipart_upload_example.pdf'

#     s3_resource.Object(bucket_name, key).upload_file(file_path,
#                             ExtraArgs={'ContentType': 'text/pdf'},
#                             Config=config,
#                             Callback=ProgressPercentage(file_path)
#                             )

##########

# import boto3
# from boto3.s3.transfer import TransferConfig


# s3_client = boto3.client('s3')

# S3_BUCKET = 'mybucket'
# FILE_PATH = '/path/to/file/'
# KEY_PATH = "/path/to/s3key/" 

# def uploadFileS3(filename):
#     config = TransferConfig(multipart_threshold=1024*25, max_concurrency=10,
#                         multipart_chunksize=1024*25, use_threads=True)
#     file = FILE_PATH + filename
#     key = KEY_PATH + filename
#     s3_client.upload_file(file, S3_BUCKET, key,
#     ExtraArgs={ 'ACL': 'public-read', 'ContentType': 'video/mp4'},
#     Config = config,
#     Callback=ProgressPercentage(file)
#     )

# uploadFileS3('upload.mp4')

##########

# from django.conf import settings
# from django.shortcuts import render
# from urllib.parse import urlparse

# import boto3


# def multipart_video_upload(self, request):
#     s3client = boto3.client(
#         's3',
#         aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
#         aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
#         region_name=settings.AWS_S3_REGION_NAME
#     )
#     bucket = settings.AWS_STORAGE_BUCKET_NAME


#     # Create multipart upload
#     multipart_upload = s3client.create_multipart_upload(
#         ACL='public-read',
#         Bucket=bucket,
#         ContentType='video/mp4',
#         Key='movie.mp4',
#     )

#     with open('movie.mp4', 'rb') as f:
#         while True:
#             piece = f.read(10000000) # roughly 10 mb parts
#             if piece == b'':
#                 break